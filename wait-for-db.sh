#!/bin/bash

set -e

host="$1"
port="$2"
shift 2
cmd="$@"

echo "Waiting for external PostgreSQL database at $host:$port..."
echo "Database details: DB_NAME=$DB_NAME, DB_USER=$DB_USER, DB_HOST=$host, DB_PORT=$port"

# Check if we can resolve the hostname
echo "Checking if we can resolve the hostname $host..."
if command -v nslookup >/dev/null 2>&1; then
  nslookup $host || echo "Failed to resolve hostname $host"
else
  echo "nslookup not available, trying ping"
  ping -c 1 $host || echo "Failed to ping hostname $host"
fi

# Check if we can connect to the port
echo "Checking if we can connect to port $port on $host..."
if command -v nc >/dev/null 2>&1; then
  timeout 5 nc -zv $host $port || echo "Failed to connect to $host:$port"
else
  echo "nc not available, skipping port check"
fi

# Try to connect to the database with a timeout
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
  echo "Attempt $attempt/$max_attempts: Trying to connect to PostgreSQL database..."
  if PGPASSWORD=$DB_PASSWORD psql -h "$host" -U "$DB_USER" -p "$port" -d "$DB_NAME" -c '\q' 2>&1; then
    >&2 echo "Successfully connected to PostgreSQL database at $host:$port"
    break
  else
    error_output=$(PGPASSWORD=$DB_PASSWORD psql -h "$host" -U "$DB_USER" -p "$port" -d "$DB_NAME" -c '\q' 2>&1)
    >&2 echo "Connection failed with error: $error_output"
  fi

  attempt=$((attempt+1))
  >&2 echo "PostgreSQL database at $host:$port is not available yet - sleeping 2 seconds"
  sleep 2

  # If we've reached the maximum number of attempts, but still want to continue
  if [ $attempt -eq $max_attempts ]; then
    >&2 echo "Warning: Could not connect to PostgreSQL database after $max_attempts attempts"
    >&2 echo "The application may not function correctly if the database is not available"
    >&2 echo "Proceeding anyway..."
  fi
done

>&2 echo "Executing command: $cmd"
exec $cmd
