# Git
.git
.gitignore

# Docker
.docker
Dockerfile
docker-compose.yml
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environment
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Local development
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Static files
static/
media/

# Database
*.sqlite3
