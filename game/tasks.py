from celery import shared_task
from admin_controller.models import ConstantTable
# from gameshow_hustle.models import Contestants


@shared_task
def run_stage_one_activity_one_setup(game_nick):
    """
    This function sets up the stage one activity one.
    """
    from accounts.models import ContestantsWallets
    from game.models import Games, Contestants, SharingPercentageLog
    from game.enums import GameStatus, GameStageChoices

    activity_1_avail_sharing_amount = ConstantTable.objects.last().hustle_activity_one_sharing_amount
    activity_2_avail_sharing_amount = ConstantTable.objects.last().hustle_activity_two_sharing_amount

    # Create Game instance
    game_instance = Games.create_game(game_nick=game_nick, game_status=GameStatus.IN_PROGESS)

    if isinstance(game_instance, dict) and game_instance.get("status") == "failed":
        return game_instance
    # Create six contestants
    contestant_names = ["contestant_1", "contestant_2", "contestant_3", "contestant_4", "contestant_5", "contestant_6"]

    contestants = Contestants.create_contestants(
        game_episode=game_instance.game_episode,
        contestant_names=contestant_names,
    )
    
    for contestant in contestants:
        ContestantsWallets.objects.create(contestant=contestant)
    

    return {
        "status": "success",
        "message": "Game started successfully"
        }

    # # Generate activity one sharing percentages for all contestants totalling 100%
    # sharing_percentages = SharingPercentageLog.generate_sharing_percentage(
    #     game_episode=game_instance.game_episode,
    #     game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_ONE,
    #     available_sharing_amount=activity_1_avail_sharing_amount,
    # )

    # # Generate the amount value for each number pick
    # SharingPercentageLog.generate_number_picks_amounts_values(
    #     game_episode=game_instance.game_episode, game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_ONE
    #     )

    # # Gnerate sharing Percentage for Blind Hustle
    # sharing_percentages = SharingPercentageLog.generate_sharing_percentage(
    #     game_episode=game_instance.game_episode,
    #     game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_TWO,
    #     available_sharing_amount=activity_2_avail_sharing_amount,
    # )

    # Generate the amount value for each hustle pick
    # SharingPercentageLog.generate_number_picks_amounts_values(
    #     game_episode=game_instance.game_episode, game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_TWO
    #     )

    # Generate the multiplier for each hustle
    # SharingPercentageLog.generate_number_picks_amounts_values(
    #     game_id=game_instance.id, game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_TWO
    #     )


    # Split each winners percentage across five number picks totalling 100% of the contestant's share percentage
    # Create activity summary
