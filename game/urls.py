from django.urls import path

from game import views


urlpatterns = [
    path("answer_hustle_reveal_question/<int:question_id>", views.AnswerHustleRevealQuestions.as_view(), name="answer_question"),
    path("get_hustle_reveal_answers", views.GetConstestantAnswers.as_view(), name="get_question_answers"),
    path("get_questions", views.GetQuestions.as_view({"get": "list"}), name="get_question"),
    path("retrieve_question/<int:pk>", views.GetQuestions.as_view({"get": "retrieve"}), name="get_question_by_id"),
    path("stage_one_activity_one_ranking/", views.StageOneActivityOneRankingView.as_view(), name="activity_one_ranking"),
    path("hustle_picks/<int:game_episode>/", views.HustlePicksView.as_view(), name="hustle_picks"),
    path("get_hustle_picks/<int:game_episode>/", views.GetHustlePicksView.as_view(), name="get_hustle_picks"),
    path("generate_hustle_reveal/<int:game_episode>/", views.GenerateHustleReveal.as_view(), name="generate_hustle_reveal"),
    path("get_hustle_reveal/<int:game_episode>/", views.GetHustleReveal.as_view(), name="get_hustle_reveal"),
    path("stage_one_activity_two_hustle_picks/", views.StageOneActivityTwoHustlePicksView.as_view(), name="activity_two_hustle_picks"),
    path("blind_hustle_investment_percentages/", views.BlindHustleInvestmentPercentagesView.as_view(), name="blind_hustle_investment_percentages"),
    path("get_hustle_questions/<int:game_episode>", views.GetHustleQuestions.as_view(), name="get_hustle_questions"),
    path("question_start_time/", views.QuestionStartTime.as_view(), name="question_start_time"),
    # path("start_gameshow_hustle/", views.StartHustleGameView.as_view(), name="start_game_hustle"),
    path("stage_one_activity_one_number_picks/", views.StageOneActivityOneNumberPicksView.as_view(), name="activity_one_number_picks"),
    path("get_all_proof_questions/<int:game_episode>", views.GetProofHustleQuestions.as_view(), name="get_all_proof_questions"),
    path("answer_proof_hustle_questions/<int:question_id>", views.AnswerProofHustleQuestions.as_view(), name="answer_proof_hustle_questions"),
    path("get_proof_hustle_answers", views.GetProofHustleAnswers.as_view(), name="get_proof_hustle_answers"),
    path("debit_for_proof_hustle", views.DebitForProofHustleQuestions.as_view(), name="debit_for_proof_hustle"),
    path("request_next_question/<int:game_episode>", views.RequestHustNextQuestion.as_view(), name="request_next_question"),
    path("s1_question_time_elapsed/<int:question_id>", views.HustleQuestionTimeElapsed.as_view(), name="s1_question_time_elapsed"),
    path("end_stage_one/<int:game_episode>", views.EndStageOne.as_view(), name="end_stage_one"),
    # path("initiate_stage_two/<int:game_episode>", views.InitializeStageTwo.as_view(), name="initiate_stage_two")
    path("s2_question_time_elapsed/<int:question_id>", views.ProofQuestionTimeElapsed.as_view(), name="s2_question_time_elapsed"),
    path("handle_wallet_funding_and_debits/", views.HandleWinnersFundingView.as_view(), name="handle_wallet_funding"),
    path("end_stage_two/<int:game_episode>", views.EndStageTwo.as_view(), name="end_stage_one"),
]
