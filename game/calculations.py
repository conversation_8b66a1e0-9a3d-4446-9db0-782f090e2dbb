from decimal import ROUND_DOWN, ROUND_HALF_UP, Decimal
import logging
import random

from django.db import IntegrityError

from accounts.models import ContestantQuestionSpend, Contestants, ContestantsWallets
from game.models import ContestsAnswers, Questions
from game.utils import get_highest_spend_key

def process_hustle_question_breakdown(game_episode: int, question: Questions, question_count: int):
    # Call this for prep event
    # And every question time elapsed
    
    logs = logging.getLogger("")
    """
    Process and store contestant question spend data for a given game episode.
    """

    contestants = Contestants.objects.filter(game_episode=game_episode)

    booster = question.question_booster
    multiplier = {
        "X2": 2,
        "X3": 3,
        "X4": 4,
        "X5": 5,
    }.get(booster, 1)

    result = []

    for contestant in contestants:
        try:
            wallet = ContestantsWallets.objects.get(contestant=contestant)
        except ContestantsWallets.DoesNotExist:
            logs.error(
                f"QUESTION SPEND: Wallet for contestant {contestant.id} not found."
            )
            # message = f"Wallet for contestant {contestant.id} not found"
            print("NO WALLET")
            continue

        if wallet.wallet_balance <= 0:
            logs.info(
                f"QUESTION SPEND: Wallet Balance is <=0 -> {wallet.wallet_balance}."
            )
            continue
            

        # Step 5: Calculate max_question_spend
        max_question_spend = (wallet.wallet_balance / Decimal(question_count)).quantize(
            Decimal("0.01"), rounding=ROUND_DOWN
        )

        # Step 6: Create four progressive parts: 25%, 50%, 75%, 100%
        parts = [
            (max_question_spend * Decimal("0.25")).quantize(
                Decimal("0.01"), rounding=ROUND_DOWN
            ),
            (max_question_spend * Decimal("0.50")).quantize(
                Decimal("0.01"), rounding=ROUND_DOWN
            ),
            (max_question_spend * Decimal("0.75")).quantize(
                Decimal("0.01"), rounding=ROUND_DOWN
            ),
            max_question_spend,  # Already quantized
        ]
        print(f"PARTS >> {parts}")

        # Step 7: Multiply each by booster
        spend_breakdown = {
            str(part): float((part * Decimal(multiplier)).quantize(Decimal("0.01")))
            for part in parts
        }

        # Step 8: Save to DB
        try:
            ContestantQuestionSpend.objects.create(
                game_episode=game_episode,
                question_id=question.id,
                contestant_id=contestant.id,
                spend_breakdown=spend_breakdown,
            )
        except IntegrityError:
            logs.error(
                f"QUESTION SPEND: Contestant {contestant.id} has already spent on this question."
            )
            # Maybe get the question spend object and update it here
            spend = ContestantQuestionSpend.objects.filter(
                game_episode=game_episode,
                question_id=question.id,
                contestant_id=contestant.id,
            ).first()
            spend.spend_breakdown = spend_breakdown
            spend.save()
            pass

        # Step 9: Add to result list
        result.append(
            {
                "contestant_id": contestant.id,
                "contestant_name": contestant.name,
                "wallet_balance": float(wallet.wallet_balance),
                "max_question_spend": float(max_question_spend),
                # "booster": booster,
                "spend_breakdown": spend_breakdown,
            }
        )
    
    return result


