import logging
import os
import sys

import time
from django.apps import AppConfig

from django.conf import settings
from library.mqtt_service import MQTTService
import logging

logs = logging.getLogger("SOCKET COMMUNICATION")

mqtt_instance = MQTTService()

# def listen_to_topic(topic, qos=0):
    
#     with mqtt_instance.session():
#         subscribed = mqtt_instance.subscribe(topic, qos)
#         # if not subscribed:
#         #     logs.error(f"Could not subscribe to topic: {topic}")
#         #     return
#         logs.info(f"Listening for messages on topic: {topic}")
#         try:
#             # Keep the script running to receive messages
#             while True:
#                 time.sleep(1)
#         except KeyboardInterrupt:
#             logs.info("Stopped listening due to keyboard interrupt")


class GameConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'game'
    
    def ready(self):
        """
        This method is called when the application is ready.
        You can perform any initialization tasks here.
        """
         # Check if this is the main process running the server
        # This prevents duplicate connections in development when using auto-reload
        import game.signals
        if os.environ.get('RUN_MAIN', None) != 'true' and 'runserver' in sys.argv:
            return
        
        logs = logging.getLogger('GAME APP')
        logs.info("GAME APP IS READY >>>>>>>> >>>>>>")
        # listen_to_topic(settings.MQTT_TOPIC)

        mqtt_instance.connect()
        mqtt_instance.loop_start()
        mqtt_instance.subscribe(settings.MQTT_TOPIC)
        
        mqtt_instance.client.loop_start()
        
