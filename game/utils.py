from decimal import ROUND_DOWN, Decimal


def get_serializer_key_error(errors_dict: dict):
    try:
        key = list(errors_dict)[0]
        error = errors_dict.get(key)
        return f"{error[0]}"
    except Exception:
        return ""
 

    
def is_hustle_reveal_completed(game_episode):
    # return true if all 6 contestants have 5 hustle generate picks
    # return for only a game
    return True

def is_hustle_picks_completed(game_episode):
    # return true if all 6 contestants have 5 hustle generate picks
    # return for only a game
    return True