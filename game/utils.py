from decimal import ROUND_DOWN, Decimal


def get_serializer_key_error(errors_dict: dict):
    try:
        key = list(errors_dict)[0]
        error = errors_dict.get(key)
        return f"{error[0]}"
    except Exception:
        return ""


def get_highest_spend_key(spend_breakdown: dict) -> Decimal:
    if not spend_breakdown:
        return None
    return max(Decimal(k) for k in spend_breakdown.keys()) 

def find_amount_key_in_spend_breakdown(spend_breakdown: dict, amount) -> Decimal:
    """
    Check if the given amount (as float or Decimal) exists as a key in spend_breakdown.
    Returns the Decimal value of the amount if found, else None.
    """
    if not spend_breakdown:
        print("spend breakdown is empty")
        return None

    print(f"AMOUNNT FROM FUNCTION {amount}")
    amount_str = str(amount)
    

    if amount_str in spend_breakdown:
        return Decimal(amount_str)
    print(f"Amount {amount_str} not found in spend breakdown")
    return None
    
def is_hustle_reveal_completed(game_episode):
    # return true if all 6 contestants have 5 hustle generate picks
    # return for only a game
    return True

def is_hustle_picks_completed(game_episode):
    # return true if all 6 contestants have 5 hustle generate picks
    # return for only a game
    return True