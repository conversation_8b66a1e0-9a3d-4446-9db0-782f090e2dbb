# signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver

from game.socket_comm import all_hustle_picks
from .models import HustlePicks

@receiver(post_save, sender=HustlePicks)
def handle_hustle_picks_save(sender, instance, created, **kwargs):
    """
    Signal handler for HustlePicks model.
    
    Runs a custom function when a HustlePicks instance is saved.
    - `created`: True if a new record was created.
    - `instance`: The HustlePicks instance being saved.
    """
    game_episode = instance.game_episode
    all_hustle_picks(game_episode)