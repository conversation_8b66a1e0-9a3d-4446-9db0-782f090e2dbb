from django.db import models
from django.utils.translation import gettext_lazy as _


class GameStatus(models.TextChoices):
    IN_ACTIVE = _("IN_ACTIVE"), "IN_ACTIVE"
    IN_PROGESS = _("IN_PROGESS"), "IN_PROGRESS"
    COMPLETED = _("COMPLETED"), "COMPLETED"

class GameStageChoices(models.TextChoices):
    STAGE_ONE = _("STAGE_ONE"), "STAGE_ONE"
    STAGE_ONE_ACTIVITY_ONE = _("STAGE_ONE_ACTIVITY_ONE"), "STAGE_ONE_ACTIVITY_ONE"
    STAGE_ONE_ACTIVITY_TWO = _("STAGE_ONE_ACTIVITY_TWO"), "STAGE_ONE_ACTIVITY_TWO"
    STAGE_TWO = _("STAGE_TWO"), "STAGE_TWO"
    STAGE_THREE = _("STAGE_THREE"), "STAGE_THREE"
    STAGE_FOUR = _("STAGE_FOUR"), "STAGE_FOUR"
    STAGE_FIVE = _("STAGE_FIVE"), "STAGE_FIVE"

class ContestantAtrrChoices(models.TextChoices):
    contestant_1 = _("contestant_1"), "contestant_1"
    contestant_2 = _("contestant_2"), "contestant_2"
    contestant_3 = _("contestant_3"), "contestant_3"
    contestant_4 = _("contestant_4"), "contestant_4"
    contestant_5 = _("contestant_5"), "contestant_5"
    contestant_6 = _("contestant_6"), "contestant_6"

class HustleBoosterChoices(models.TextChoices):
    X2 = _("X2"), "X2"
    X3 = _("X3"), "X3"
    X4 = _("X4"), "X4"
    X5 = _("X5"), "X5"

class HustleStateChoices(models.TextChoices):
    DUD = _("DUD"), "DUD"
    OPPORTUNITY = _("OPPORTUNITY"), "OPPORTUNITY"

class HustleBusinessChoices(models.TextChoices):
    FINTECH = _("FINTECH"), "Financial Technology"
    FASHION = _("FASHION"), "Fashion Design"
    AGRITECH = _("AGRITECH"), "Agricultural Technology"
    CATERING = _("CATERING"), "Catering Services"
    BAKERY = _("BAKERY"), "Bakery Business"
    LOGISTICS = _("LOGISTICS"), "Logistics Services"
    REAL_ESTATE = _("REAL_ESTATE"), "Real Estate"
    BEAUTY_SALON = _("BEAUTY_SALON"), "Beauty Salon"
    BARBER_SHOP = _("BARBER_SHOP"), "Barber Shop"
    GROCERY_STORE = _("GROCERY_STORE"), "Grocery Store"
    PHARMACY = _("PHARMACY"), "Pharmacy"
    RESTAURANT = _("RESTAURANT"), "Restaurant"
    COFFEE_SHOP = _("COFFEE_SHOP"), "Coffee Shop"
    FOOD_TRUCK = _("FOOD_TRUCK"), "Food Truck"
    CLEANING_SERVICE = _("CLEANING_SERVICE"), "Cleaning Service"
    LAUNDRY = _("LAUNDRY"), "Laundry Service"
    TECH_REPAIR = _("TECH_REPAIR"), "Tech Repair Shop"
    TUTORING = _("TUTORING"), "Tutoring Service"
    PHOTOGRAPHY = _("PHOTOGRAPHY"), "Photography Studio"
    VIDEOGRAPHY = _("VIDEOGRAPHY"), "Videography Service"
    EVENT_PLANNING = _("EVENT_PLANNING"), "Event Planning"
    INTERIOR_DESIGN = _("INTERIOR_DESIGN"), "Interior Design"
    GRAPHIC_DESIGN = _("GRAPHIC_DESIGN"), "Graphic Design"
    WEB_DEVELOPMENT = _("WEB_DEVELOPMENT"), "Web Development"
    APP_DEVELOPMENT = _("APP_DEVELOPMENT"), "App Development"
    DIGITAL_MARKETING = _("DIGITAL_MARKETING"), "Digital Marketing"
    CONTENT_CREATION = _("CONTENT_CREATION"), "Content Creation"
    CONSULTING = _("CONSULTING"), "Consulting Service"
    COACHING = _("COACHING"), "Coaching Service"
    FITNESS_STUDIO = _("FITNESS_STUDIO"), "Fitness Studio"
    YOGA_STUDIO = _("YOGA_STUDIO"), "Yoga Studio"
    BOOKSTORE = _("BOOKSTORE"), "Bookstore"
    ART_GALLERY = _("ART_GALLERY"), "Art Gallery"
    CRAFT_SHOP = _("CRAFT_SHOP"), "Craft Shop"
    JEWELRY_MAKING = _("JEWELRY_MAKING"), "Jewelry Making"
    FURNITURE_MAKING = _("FURNITURE_MAKING"), "Furniture Making"
    CLOTHING_STORE = _("CLOTHING_STORE"), "Clothing Store"
    SHOE_STORE = _("SHOE_STORE"), "Shoe Store"
    ELECTRONICS_STORE = _("ELECTRONICS_STORE"), "Electronics Store"
    TOY_STORE = _("TOY_STORE"), "Toy Store"
    
class StageChoices(models.TextChoices):
    HUSTLE_PICKS = _("HUSTLE_PICKS"), "HUSTLE PICKS"
    PROOF_YOUR_HUSTLE = _("PROOF_YOUR_HUSTLE"), "PROOF YOUR HUSTLE"
    SHOWDOWN_FOR_PASS = _("SHOWDOWN_FOR_PASS"), "SHOWDOWN FOR PASS"
    GOLDEN_HUSTLE_MATCH = _("GOLDEN_HUSTLE_MATCH"), "GOLDEN HUSTLE MATCH"