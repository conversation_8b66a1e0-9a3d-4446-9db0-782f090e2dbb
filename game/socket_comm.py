"""
This module is to real time communicaions in place of API communications for the game app.
"""

import datetime
import decimal
import json
import logging
import random

from django.db import IntegrityError

from admin_controller.models import ConstantTable, HustleNames
from game.apps import mqtt_instance
from django.conf import settings
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP
from django.db import transaction
from django.utils import timezone


from accounts.models import ContestantQuestionSpend, Contestants, ContestantsWallets
from accounts.enums import WalletTransactionDescription

from game.enums import (
    GameStatus,
    HustleBoosterChoices,
    HustleBusinessChoices,
    HustleStateChoices,
    StageChoices
)
from game.models import (
    ContestsAnswers,
    Games,
    HustlePicks,
    HustleReveal,
    ProofHustleQuestionAnswer, Questions,
    ProofHustleQuestion,
    StageProgress,
    StageChoices,
)
logs = logging.getLogger("SOCKET COMMUNICATION")
from game.utils import get_highest_spend_key

def hustle_picks_communication(json_data: dict) -> str:
    """
    Socket communication for hustle picks.
    """
    logs = logging.getLogger("SOCKET COMMUNICATION")
    data = json_data.get("payload")

    game_episode = data.get("game_episode")
    contestant_id = data.get("contestant_id")
    number_picks = data.get("pick")

    if number_picks < 1 or number_picks > 49:
        logs.error(
            f"HUSTLE PICKS: Invalid number of picks {number_picks}. Must be between 1 and 49."
        )
        return

    try:
        game = Games.objects.get(
            game_episode=game_episode, status=GameStatus.IN_PROGESS
        )
    except Games.DoesNotExist:
        logs.error(f"HUSTLE PICKS: Game {game_episode} not found.")
        return

    if game.status != GameStatus.IN_PROGESS:
        logs.error(f"HUSTLE PICKS: Game {game_episode} is not in progress.")
        return

    try:
        contestant = Contestants.objects.get(
            id=contestant_id, game_episode=game_episode
        )
    except Contestants.DoesNotExist:
        logs.error(
            f"HUSTLE PICKS: Contestant {contestant_id} not found in game episode {game_episode}."
        )
        return

    hustle_picks, created = HustlePicks.objects.get_or_create(
        game_episode=game_episode, contestant_id=contestant_id
    )
    if isinstance(hustle_picks.number_picks, str):
        try:
            number_picks_list = eval(hustle_picks.number_picks)
        except (SyntaxError, NameError):
            number_picks_list = []
    else:
        number_picks_list = hustle_picks.number_picks or []

    if not created and number_picks in number_picks_list:
        number_picks_list.remove(number_picks)
        hustle_picks.number_picks = number_picks_list
        hustle_picks.save()
        logs.info(
            f"HUSTLE PICKS: Number {number_picks} removed from contestant {contestant_id}'s picks."
        )
        return

    #  Check if number_picks is already picked by another contestant in the same game
    conflicting_pick = (
        HustlePicks.objects.filter(game_episode=game_episode)
        .exclude(contestant_id=contestant_id)
        .filter(number_picks__contains=[number_picks])
        .first()
    )

    if conflicting_pick:
        logs.error(
            f"HUSTLE PICKS: Number {number_picks} is already selected by another contestant."
        )
        return

    # Proceed to add the pick
    if len(number_picks_list) < 5:
        number_picks_list.append(number_picks)
        hustle_picks.number_picks = number_picks_list
        hustle_picks.save()
        logs.info(f"HUSTLE PICKS: Successfully selected {number_picks}.")
    else:
        logs.error(f"Contestant {contestant_id} has already selected 5 picks.")
        return


def all_hustle_picks(game_episode):
    logs = logging.getLogger("SOCKET COMMUNICATION")

    hustle_picks = HustlePicks.objects.filter(game_episode=game_episode)
    response_data = []
    for pick in hustle_picks:
        if isinstance(pick.number_picks, str):
            try:
                number_picks_list = eval(pick.number_picks)
            except (SyntaxError, NameError):
                number_picks_list = []
        else:
            number_picks_list = pick.number_picks or []

        response_dict = {
            "contestant_id": pick.contestant_id,
            "picks": number_picks_list,
        }
        response_data.append(response_dict)

    payload = {"event": "receive_hustle_picks", "payload": response_data}
    mqtt_instance.publish(topic=settings.MQTT_TOPIC, payload=json.dumps(payload))
    logs.info(f"GET PICKS: Published All Hustle Picks >> {payload}")
    return


# 1. Publish from signal on save or create
# 2. Make mapping of functions to event titles work like url or dns mapping
# 3. Optimize quieries and test or check times it takes to complete(also log these)


def generate_hustle_reveals(game_episode: int, game: Games):

    logs = logging.getLogger("SOCKET COMMUNICATION")

    hustle_picks = HustlePicks.objects.filter(game_episode=game_episode)

    results = []
    for pick in hustle_picks:
        contestant_id = pick.contestant_id

        if isinstance(pick.number_picks, str):
            try:
                number_picks_list = eval(pick.number_picks)
            except (SyntaxError, NameError):
                number_picks_list = []
        else:
            number_picks_list = pick.number_picks or []

        if not number_picks_list:
            continue

        number_picks_list = number_picks_list[:5]

        # Determine state distribution: 3 duds and 2 opportunities
        states = [HustleStateChoices.DUD] * 3 + [HustleStateChoices.OPPORTUNITY] * 2
        # Shuffle the states to randomize which picks are duds vs opportunities
        random.shuffle(states)

        # Get random business names without repetition
        business_names = list(HustleNames.objects.all())
        random.shuffle(business_names)

        contestant_reveals = []

        # Create HustleReveal objects for each pick
        try:
            for i, number in enumerate(number_picks_list):
            
                hustle_reveal = HustleReveal.objects.filter(
                    game_episode=game_episode,
                    hustle_pick__isnull=True
                ).first()
            
                hustle_reveal.hustle_pick=pick
                hustle_reveal.hustle_name=business_names[i].name
                hustle_reveal.hustle_number=number
                hustle_reveal.hustle_state=states[i]
            
                hustle_reveal.contestant_id=contestant_id
                hustle_reveal.save()
                
                contestant = Contestants.objects.get(id=contestant_id)
                contestant_wallet = ContestantsWallets.objects.get(contestant=contestant)
                contestant_wallet.wallet_balance = decimal.Decimal(str(hustle_reveal.hustle_amount)) + contestant_wallet.wallet_balance  
                contestant_wallet.save()
                
                if hustle_reveal.hustle_state == HustleStateChoices.OPPORTUNITY:
                    question = Questions.objects.filter(
                        game=game,
                        hustle_reveal__isnull=True
                    ).first()
                    print(f"QUESTION DETAILS >> {question}")
                    question.hustle_reveal = hustle_reveal
                    question.save()
                
                contestant_reveals.append({
                    "id": hustle_reveal.id,
                    "hustle_name": hustle_reveal.hustle_name,
                    "hustle_number": hustle_reveal.hustle_number,
                    "hustle_state": hustle_reveal.hustle_state,
                    # "hustle_booster": hustle_reveal.hustle_booster,
                    "hustle_amount": float(hustle_reveal.hustle_amount)
                })

            results.append({
                "contestant_id": contestant_id,
                "reveals": contestant_reveals
            })
        except Exception as err:
            logs.error(f"GENERATE REVEAL: {err}")
            
        logs.info(f"GENERATE REVEAL: Successfully generated hustle reveal")


def hustle_pick_time_elapsed(json_data: dict):
    """
    Randomly assigns available picks to contestants in a game
    who have less than 5 picks, only if total game picks is less than 30.
    """
    logs = logging.getLogger("SOCKET COMMUNICATION")

    data = json_data.get("payload")
    game_episode = data.get("game_episode")

    try:
        game = Games.objects.get(
            game_episode=game_episode, status=GameStatus.IN_PROGESS
        )
    except Games.DoesNotExist:
        logs.error(f"HUSTLE PICKS: Game episode {game_episode} does not exist.")
        return

    all_hustle_picks = HustlePicks.objects.filter(game_episode=game_episode)
    all_used_numbers = set()

    # Collect all already-used pick numbers
    for hp in all_hustle_picks:
        picks = hp.number_picks or []
        all_used_numbers.update(picks)

    if len(all_used_numbers) >= 30:
        logs.info(
            f"HUSTLE PICKS: Already have {len(all_used_numbers)} picks. Proceeding to generate reveals."
        )
        generate_hustle_reveals(game_episode=game_episode, game=game)
        return

    available_numbers = list(set(range(1, 50)) - all_used_numbers)
    random.shuffle(available_numbers)

    # Loop through all contestants in this game
    contestants = Contestants.objects.filter(game_episode=game_episode)
    for contestant in contestants:
        try:
            hustle_pick, _ = HustlePicks.objects.get_or_create(
                game_episode=game_episode, contestant_id=contestant.id
            )
        except Exception as e:
            logs.error(
                f"HUSTLE PICKS: Error fetching/creating HustlePicks for contestant {contestant.id}: {str(e)}"
            )
            continue

        current_picks = hustle_pick.number_picks or []
        picks_needed = 5 - len(current_picks)

        if picks_needed <= 0:
            continue

        # Check if enough available numbers left
        if len(all_used_numbers) >= 30:
            logs.info(f"HUSTLE PICKS: Stopped assignment, reached 30 total picks.")
            break

        # Select picks ensuring total game picks <= 30
        picks_to_assign = []
        for num in available_numbers:
            if num in current_picks:
                continue
            if num in all_used_numbers:
                continue
            picks_to_assign.append(num)
            all_used_numbers.add(num)
            if len(all_used_numbers) >= 30 or len(picks_to_assign) == picks_needed:
                break

        if picks_to_assign:
            current_picks.extend(picks_to_assign)
            hustle_pick.number_picks = current_picks
            hustle_pick.save()
            logs.info(
                f"HUSTLE PICKS: Randomly assigned picks {picks_to_assign} to contestant {contestant.id}"
            )

    generate_hustle_reveals(game_episode=game_episode, game=game)


def start_game_episode(json_data: dict):
    """
    Starts the hustle game by publishing the start event.
    """
    logs = logging.getLogger("SOCKET COMMUNICATION")

    data = json_data.get("payload")
    game_episode = data.get("game_episode")

    try:
        game = Games.objects.get(game_episode=game_episode, status=GameStatus.IN_ACTIVE)
    except Games.DoesNotExist:
        payload = {"event": "receive_game_start", "payload": "THE GAME STATUS RECEIVED BUT NOT EXIST OR ALREADY IN PROGRESS >>>>>>>>>>>>>> "}
        mqtt_instance.publish(topic=settings.MQTT_TOPIC, payload=json.dumps(payload))
        logs.error(f"START GAME: Game episode {game_episode} does not exist.")
        return

    game.status = GameStatus.IN_PROGESS
    game.save()
    
    contestants = Contestants.objects.filter(game_episode=game_episode)
    for contestant in contestants:
        try:
            StageProgress.objects.create(
                game_episode=game.game_episode,
                stage=StageChoices.HUSTLE_PICKS,  
                contestant=contestant,
                entered_at=timezone.now(),
                pot_at_entry=0.0
            )
        except IntegrityError:
            logs.error(f"START GAME: Unable to create Stage progress for contestant {contestant.id}")
    
    payload = {"event": "receive_game_start", "payload": "THE GAME STATUS HAS BEEN UPDATE >>>>>>>>>>>>>> "}
    mqtt_instance.publish(topic=settings.MQTT_TOPIC, payload=json.dumps(payload))
    logs.info(f"START GAME: Game episode {game_episode} status updated to IN_PROGRESS.")
    
    

def process_and_store_question_spend(json_data: dict):
    # Call this for prep event
    # And every question time elapsed
    """
    Process and store contestant question spend data for a given game episode.
    """
    logs = logging.getLogger("SOCKET COMMUNICATION")
    # Step 1: Filter unasked questions for the game_episode

    data = json_data.get("payload")
    game_episode = data.get("game_episode")
    question_id = data.get("question_id")
    unasked_questions = Questions.objects.filter(
        asked=False, game__game_episode=game_episode
    )

    # Step 2: Count of unasked questions
    question_count = unasked_questions.count()
    print(f"QUESTION COUNT >> {question_count}")
    if question_count == 0:
        logs.info(
            f"QUESTION SPEND: All questions have been asked for game episode {game_episode}."
        )
        return

    # Step 3: Get all contestants for the game_episode
    contestants = Contestants.objects.filter(game_episode=game_episode)

    try:
        question = unasked_questions.get(id=question_id)
    except Questions.DoesNotExist:
        logs.error(
            f"QUESTION SPEND: Question ID {question_id} not found among unasked questions."
        )
        return

    # Step 4: Get booster multiplier
    booster = question.question_booster
    print(f"BOOSTER >> {booster}")
    multiplier = {
        "X2": 2,
        "X3": 3,
        "X4": 4,
        "X5": 5,
    }.get(booster, 1)

    result = []

    for contestant in contestants:
        try:
            wallet = ContestantsWallets.objects.get(contestant=contestant)
        except ContestantsWallets.DoesNotExist:
            logs.error(
                f"QUESTION SPEND: Wallet for contestant {contestant.id} not found."
            )
            continue

        if wallet.wallet_balance <= 0:
            logs.info(
                f"QUESTION SPEND: Wallet Balance is <=0 -> {wallet.wallet_balance}."
            )
            continue

        # Step 5: Calculate max_question_spend
        max_question_spend = (wallet.wallet_balance / Decimal(question_count)).quantize(
            Decimal("0.01"), rounding=ROUND_DOWN
        )

        # Step 6: Create four progressive parts: 25%, 50%, 75%, 100%
        parts = [
            (max_question_spend * Decimal("0.25")).quantize(
                Decimal("0.01"), rounding=ROUND_DOWN
            ),
            (max_question_spend * Decimal("0.50")).quantize(
                Decimal("0.01"), rounding=ROUND_DOWN
            ),
            (max_question_spend * Decimal("0.75")).quantize(
                Decimal("0.01"), rounding=ROUND_DOWN
            ),
            max_question_spend,  # Already quantized
        ]

        # Step 7: Multiply each by booster
        spend_breakdown = {
            str(part): float((part * Decimal(multiplier)).quantize(Decimal("0.01")))
            for part in parts
        }
        print(f"SPEND BREAKDOWN >> {spend_breakdown}")

        # Step 8: Save to DB
        try:
            ContestantQuestionSpend.objects.create(
                game_episode=game_episode,
                question_id=question_id,
                contestant_id=contestant.id,
                spend_breakdown=spend_breakdown,
            )
        except IntegrityError:
            logs.error(
                f"QUESTION SPEND: Contestant {contestant.id} has already spent on this question."
            )
            pass

        # Step 9: Add to result list
        result.append(
            {
                "contestant_id": contestant.id,
                "contestant_name": contestant.name,
                "wallet_balance": float(wallet.wallet_balance),
                "max_question_spend": float(max_question_spend),
                "booster": booster,
                "spend_breakdown": spend_breakdown,
            }
        )

    # Step 10: Return JSON string
    event_to_publish = {"event": "question_s1_spend", "payload": result}
    mqtt_instance.publish(
        topic=settings.MQTT_TOPIC, payload=json.dumps(event_to_publish)
    )
    logs.info(f"QUESTION SPEND: Processed and Published Questions Spends.")
    return



def process_hustle_question_reesult(json_data: dict):
    from django.utils import timezone
    """
    Process the results for a given question, just defaults any contestant without answer to N
    """
    logs = logging.getLogger('SOCKET COMMUNICATION')
    
    question_id = json_data.get("payload").get("question_id")
    try:
        question = ProofHustleQuestion.objects.get(id=question_id)
    except ProofHustleQuestion.DoesNotExist:
        logs.error(f"PROOF QUESTION RESULTS: Proof Question ID {question_id} not found.")
        return

    game = question.game
    contestants_for_episode = Contestants.objects.filter(game_episode=game.game_episode)

    answered_ids = ProofHustleQuestionAnswer.objects.filter(question_id=question.id) \
                                          .values_list('contestant_id', flat=True)
    for contestant in contestants_for_episode.exclude(id__in=answered_ids):
        ProofHustleQuestionAnswer.objects.create(
            question_id=question.id,
            contestant_id=contestant.id,
            answer='N',
            answered_at=timezone.now()
        )
    
    question.asked = True
    question.save()
    
    correct_option = question.correct_option
    answers = list(ProofHustleQuestionAnswer.objects.filter(question_id=question.id))
                                      
    correct_answers = [ans for ans in answers if ans.answer == correct_option]
    
    winner = None
    if correct_answers:
        earliest_time = min(ans.answered_at for ans in correct_answers)
        tied_earliest = [ans for ans in correct_answers if ans.answered_at == earliest_time]

        if len(tied_earliest) > 1:
            winning_answer = random.choice(tied_earliest)
        else:
            winning_answer = tied_earliest[0]
        winner = winning_answer.contestant_id

        question.won_by = winner
        question.save()
        
    logs.info(f"PROOF QUESTION SPEND: Processed Successfully.")
    return 
    
def process_question_results(json_data: dict):
    from django.utils import timezone

    """
    Process the results for a given question:
      - Fill in missing answers with 'N'.
      - Mark question as asked.
      - Determine the winner (fastest correct answer, break ties randomly).
      - Update the question.won_by field.
      - Update all contestants' wallet balances with booster or stake deduction.
    """
    logs = logging.getLogger('SOCKET COMMUNICATION')
    
    question_id = json_data.get("payload").get("question_id")
    try:
        question = Questions.objects.get(id=question_id)
    except Questions.DoesNotExist:
        logs.error(f"QUESTION RESULTS: Question ID {question_id} not found.")
        return

    game = question.game
    contestants_for_episode = Contestants.objects.filter(game_episode=game.game_episode)

    answered_ids = ContestsAnswers.objects.filter(question_id=question.id) \
                                          .values_list('contestant_id', flat=True)
    # 1. For each contestant who has NOT answered, create a default 'N' answer.
    for contestant in contestants_for_episode.exclude(id__in=answered_ids):
        ContestsAnswers.objects.create(
            question_id=question.id,
            contestant_id=contestant.id,
            answer="N",
            amount_staked=Decimal("0.00"),
            answered_at=timezone.now(),
        )

    question.asked = True
    question.save()
   
    booster = question.question_booster  
  
    multiplier = {
        "X2": 2,
        "X3": 3,
        "X4": 4,
        "X5": 5,
    }.get(booster, 1)
    print(f"MULTIPLIER >> {multiplier}")
    correct_option = question.correct_option

    answers = list(ContestsAnswers.objects.filter(question_id=question.id))
                                      
    correct_answers = [ans for ans in answers if ans.answer == correct_option]
    print(f"LEN CORRECT ANSWERS >> {len(correct_answers)}")

    winner = None
    if correct_answers:
        earliest_time = min(ans.answered_at for ans in correct_answers)
        tied_earliest = [ans for ans in correct_answers if ans.answered_at == earliest_time]

        if len(tied_earliest) > 1:
            winning_answer = random.choice(tied_earliest)
        else:
            winning_answer = tied_earliest[0]
        winner = winning_answer.contestant_id

        question.won_by = winner
        question.save()
        
    for ans in answers:
        contestant_id = ans.contestant_id
        staked = ans.amount_staked
        answer = ans.answer
        contestant = Contestants.objects.get(id=contestant_id)
        question_spend = ContestantQuestionSpend.objects.filter(contestant_id=contestant_id, question_id=question_id).first()
        
        highest_stake = get_highest_spend_key(question_spend.spend_breakdown)
        print(f"STAKE TYPE >> {type(highest_stake)}")
        
        wallet = ContestantsWallets.objects.get(contestant=contestant)
        print(f"WALLET BALANCE >> {wallet.wallet_balance}")
        
        if answer == 'N':
            new_balance = wallet.wallet_balance - highest_stake
        
        elif winner and contestant_id == winner:
            gain = staked * multiplier
            new_balance = wallet.wallet_balance + gain
            question.amount_won = gain
            question.won = True
            question.save()
            
        else:
            new_balance = wallet.wallet_balance - staked

        wallet.wallet_balance = new_balance.quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        wallet.save()

def initialize_hustle_proof_stage(json_data: dict):
   
    """
    Initialize and set up hustle proof stage (2)
    """
    logs = logging.getLogger("SOCKET COMMUNICATION")
    
    data = json_data.get("payload")
    game_episode = data.get("game_episode")
    const_table = ConstantTable.objects.last()

    try:
        game = Games.objects.get(game_episode=int(game_episode))
    except Games.DoesNotExist:
       logs.error(f"INIT STAGE TWO: Game with Episode <{game_episode}> does not exist")
    # Check that questions exist for game
    try:
        proof_hustle_questions = ProofHustleQuestion.objects.filter(game=game)
        proof_hustle_questions_count = proof_hustle_questions.count()
        expected_hustle_questions = const_table.hustle_proof_questions_count
        sharing_amount = const_table.hustle_proof_sharing_amount

        if proof_hustle_questions_count < expected_hustle_questions:
            logs.error(f"INIT STAGE TWO: Failed Proof less than expected")
        else:
            # splt winning amount randomly amongs questions
            ProofHustleQuestion.split_winning_amount(
                hustle_questions_qs=proof_hustle_questions,
                sharing_amount=sharing_amount,
                count=proof_hustle_questions_count,
            )
    except Exception as e:
        logs.error(f"INIT STAGE TWO: Failed to split winning amount: {e}")
        
    contestants = Contestants.objects.filter(game_episode=game_episode, is_eliminated=False)
    for contestant in contestants:
        wallet = ContestantsWallets.objects.filter(contestant=contestant).first()
        try:
            StageProgress.objects.create(
                game_episode=game.game_episode,
                stage=StageChoices.PROOF_YOUR_HUSTLE,  
                contestant=contestant,
                entered_at=timezone.now(),
                pot_at_entry=wallet.wallet_balance
            )
        except IntegrityError:
            logs.error(f"INIT STAGE TWO: Unable to create Stage progress for contestant {contestant.id}")
    
    logs.info(f"INIT STAGE TWO: Successfully intiated. ")


def handle_contestant_funding_and_debit(json_data: dict):
    """
    Credit contestant upon answering a question correctly and on time.
    """
    data = json_data.get("payload")
    question_id = data.get("question_id")
    giver_contestant_id = data.get("giver_contestant_id")
    credit_source = data.get("credit_source")

    {
        "question_id": 1,
        "giver_contestant_id": 22,
        "credit_source": "gameshow_float" | "",
    }

    result = {}

    giver_contestant: Contestants = None
    try:
        giver_contestant = Contestants.objects.get(id=giver_contestant_id)
    except Contestants.DoesNotExist:
        result["giver_contestant_id"] = (
            f"HUSTLE PROOF: giver contestant with id {giver_contestant_id} does not exist"
        )
        logs.info(result)

    if credit_source == "gameshow_float":
        giver_contestant = Contestants.get
        giver_wallet = ContestantsWallets.get_gameshow_wallet()

    giver_wallet: ContestantsWallets = None
    if giver_contestant:
        try:
            giver_wallet = ContestantsWallets.objects.get(contestant=giver_contestant)
        except ContestantsWallets.DoesNotExist:
            result["contestant_wallet"] = (
                f"HUSTLE PROOF: wallet does not exist for contestant with id: {giver_contestant_id}"
            )
            logs.info(result)
    else:
        result["giver_contestant_wallet"] = (
            f"HUSTLE PROOF: wallet does not exist for contestant with id: {giver_contestant_id}"
        )
        logs.info(result)

    question: ProofHustleQuestion = None
    if question_id:
        try:
            question = ProofHustleQuestion.objects.get(id=question_id)
        except ProofHustleQuestion.DoesNotExist:
            result["question"] = (
                f"HUSTLE PROOF: question does not exist for question with id: {question_id}"
            )
            logs.info(result)
    else:
        result["question"] = f"HUSTLE PROOF: question id must be provided"
        logs.info(result)

    if giver_contestant and giver_wallet and question:
        winner_contestant_id = question.won_by

        winner_contestant = None
        try:
            winner_contestant = Contestants.objects.get(id=winner_contestant_id)
        except Contestants.DoesNotExist:
            result["winner_contestant_id"] = (
                f"HUSTLE PROOF: Winner contestant with id {winner_contestant_id} does not exist"
            )
            logs.info(result)

        winner_wallet = None
        if winner_contestant:
            try:
                winner_wallet = ContestantsWallets.objects.get(
                    contestant=winner_contestant
                )
            except ContestantsWallets.DoesNotExist:
                result["winner_wallet"] = (
                    f"HUSTLE PROOF: winner wallet does not exist for contestant with id: {winner_contestant.id}"
                )
                logs.info(result)
        else:
            result["winner_contestant_wallet"] = (
                f"HUSTLE PROOF: winner wallet does not exist for contestant with id: {winner_contestant.id}"
            )
            logs.info(result)

        if giver_wallet and winner_wallet:
            # Debit giver wallet
            if credit_source == "gameshow_float":
                pass
            else:
                debit_giver = giver_wallet.deduct_wallet(
                    amount=question.winning_amount,
                    to_wallet=winner_wallet,
                    description=WalletTransactionDescription.HUSTLE_PROOF_QUESTION_WINNING,
                )

            # Fund Winner Wallet
            fund_winner = winner_wallet.fund_wallet(
                amount=question.winning_amount,
                from_wallet=giver_wallet,
                description=WalletTransactionDescription.HUSTLE_PROOF_QUESTION_WINNING,
            )
            result["funding_result"] = f"HUSTLE PROOF: wallet funding successful."
            logs.info(result)
        else:
            result["funding_result"] = (
                f"HUSTLE PROOF: wallet funding failed. Wallets not found"
            )
            logs.info(result)
            pass
    else:
        result = result
        logs.info(result)

    event_to_publish = {"event": "contestant_funding_and_debit", "payload": result}
    mqtt_instance.publish(
        topic=settings.MQTT_TOPIC, payload=json.dumps(event_to_publish)
    )
    logs.info(f"HUSTLE PROOF WINNING FUNDING: {result}")

def end_stage_one(json_data: dict):
    """
    Ends the Hustle Pick Stage and eliminates two contestants with the lowest wallet balances.
    Also updates their StageProgress records accordingly.
    """
    logs = logging.getLogger('SOCKET COMMUNICATION')

    data = json_data.get("payload")
    game_episode = data.get("game_episode")
    if not game_episode:
        logs.error("END STAGE ONE: Missing game_episode in payload.")
        return

    contestants = Contestants.objects.filter(game_episode=game_episode, is_eliminated=False)

    contestant_wallets = ContestantsWallets.objects.filter(contestant__in=contestants).select_related('contestant')
    
    for contestant_wallet in contestant_wallets:
        contestant_wallet.wallet_balance += contestant_wallet.book_balance
        contestant_wallet.book_balance = 0
        contestant_wallet.save(update_fields=["wallet_balance", "book_balance"])

    sorted_wallets = sorted(contestant_wallets, key=lambda x: x.wallet_balance)

    lowest_two = sorted_wallets[:2]
    eliminated_ids = [w.contestant.id for w in lowest_two]
    print(f"ELIMINATED IDS >> {eliminated_ids}")

    now = timezone.now()

    # Update StageProgress for all contestants of this stage **before modifying Contestant model**
    stage_progress_records = StageProgress.objects.filter(
        game_episode=game_episode,
        stage=StageChoices.HUSTLE_PICKS,
        contestant__in=contestants
    ).select_related("contestant")

    for record in stage_progress_records:
        wallet = next((w for w in contestant_wallets if w.contestant.id == record.contestant.id), None)
        record.pot_at_exit = wallet.wallet_balance if wallet else Decimal('0.00')
        record.exited_at = now
        record.was_eliminated = record.contestant.id in eliminated_ids
        record.status = "closed"
        record.save(update_fields=["pot_at_exit", "exited_at", "was_eliminated", "status"])
        logs.info(f"Updated StageProgress for {record.contestant.constestant_attr}")

    # Eliminate the lowest two contestants AFTER stage progress update
    for wallet in lowest_two:
        contestant = wallet.contestant
        contestant.is_eliminated = True
        contestant.eliminated_stage = StageChoices.HUSTLE_PICKS
        contestant.save()
        logs.info(f"Contestant {contestant.constestant_attr} eliminated in stage_one with wallet: {wallet.wallet_balance}")
    logs.info(f"END STAGE ONE: Elimination and stage closure complete for game_episode {game_episode}")


def end_stage_two(json_data: dict):
    """
    Ends the Hustle Pick Stage and eliminates two contestants with the lowest wallet balances.
    Also updates their StageProgress records accordingly.
    """
    logs = logging.getLogger('SOCKET COMMUNICATION')

    data = json_data.get("payload")
    game_episode = data.get("game_episode")
    if not game_episode:
        logs.error("END STAGE TWO: Missing game_episode in payload.")
        return

    contestants = Contestants.objects.filter(game_episode=game_episode, is_eliminated=True)

    contestant_wallets = ContestantsWallets.objects.filter(contestant__in=contestants).select_related('contestant')

    sorted_wallets = sorted(contestant_wallets, key=lambda x: x.wallet_balance)

    lowest_two = sorted_wallets[:2]
    eliminated_ids = [w.contestant.id for w in lowest_two]
    print(f"ELIMINATED IDS >> {eliminated_ids}")

    now = timezone.now()

    # Update StageProgress for all contestants of this stage **before modifying Contestant model**
    stage_progress_records = StageProgress.objects.filter(
        game_episode=game_episode,
        stage=StageChoices.PROOF_YOUR_HUSTLE,
        contestant__in=contestants
    ).select_related("contestant")

    for record in stage_progress_records:
        wallet = next((w for w in contestant_wallets if w.contestant.id == record.contestant.id), None)
        record.pot_at_exit = wallet.wallet_balance if wallet else Decimal('0.00')
        record.exited_at = now
        record.was_eliminated = record.contestant.id in eliminated_ids
        record.status = "closed"
        record.save(update_fields=["pot_at_exit", "exited_at", "was_eliminated", "status"])
        logs.info(f"Updated StageProgress for {record.contestant.constestant_attr}")

    # Eliminate the lowest two contestants AFTER stage progress update
    for wallet in lowest_two:
        contestant = wallet.contestant
        contestant.is_eliminated = True
        contestant.eliminated_stage = StageChoices.PROOF_YOUR_HUSTLE
        contestant.save()
        logs.info(f"Contestant {contestant.constestant_attr} eliminated in stage_one with wallet: {wallet.wallet_balance}")
    logs.info(f"END STAGE TWO: Elimination and stage closure complete for game_episode {game_episode}")