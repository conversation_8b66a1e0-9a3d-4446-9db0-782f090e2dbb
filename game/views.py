# Create your views here.
import logging
import random
import decimal
from rest_framework import status
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from django.db import IntegrityError
from admin_controller.models import ConstantTable
from accounts.models import ContestantQuestionSpend, Contestants as AccountsContestants, ContestantsWallets
from game.calculations import process_hustle_question_breakdown
from game.enums import (
    ContestantAtrrChoices,
    GameStatus,
    GameStageChoices,
    HustleBoosterChoices,
    HustleBusinessChoices,
    HustleStateChoices,
    StageChoices,
)
from game.utils import find_amount_key_in_spend_breakdown, get_highest_spend_key, get_serializer_key_error, is_hustle_reveal_completed

from .models import (
    Contestants,
    ContestsAnswers,
    Games,
    HustlePicks,
    HustleReveal,
    Questions,
    SharingPercentageLog,
    ProofHustleQuestion,
    ProofHustleQuestionAnswer,
    StageProgress,
)
from .serializers import (
    ContestantAnswerSerializer,
    DebitForProofHustleQuestionSerializer,
    GetQuestionsSerializer,
    HustlePicksResponseSerializer,
    PicksSerializer,
    QuestionStartTimeSerializer,
    ProofContestantAnswerSerializer,
)

import ast


from django.db import IntegrityError

from admin_controller.models import ConstantTable, HustleNames
from game.apps import mqtt_instance
from django.conf import settings
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP
from django.db import transaction
from django.utils import timezone


from accounts.models import ContestantQuestionSpend, Contestants, ContestantsWallets
from accounts.enums import WalletTransactionDescription

from game.enums import (
    GameStatus,
    HustleBoosterChoices,
    HustleBusinessChoices,
    HustleStateChoices,
    StageChoices
)
from game.models import (
    ContestsAnswers,
    Games,
    HustlePicks,
    HustleReveal,
    ProofHustleQuestionAnswer, Questions,
    ProofHustleQuestion,
    StageProgress,
    StageChoices,
)


class AnswerHustleRevealQuestions(APIView):
    def post(self, request, question_id: int):

        try:
            question = Questions.objects.get(id=question_id)
        except Questions.DoesNotExist:
            data = {
                "status": "failed",
                "message": f"Question with id `{question_id}` does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if question.asked:
            data = {"status": "failed", "message": "Question Already Asked"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
 
        # ENSURE TO CONFIRM THE AMOUNT STAKED IF IT TALLIES WITH THE AMOUNT GENERATED
        serializer = ContestantAnswerSerializer(data=request.data)
        if serializer.is_valid():
            answer = serializer.validated_data.get("answer")
            contestant_id = serializer.validated_data.get("contestant_id")
            timestamp = serializer.validated_data.get("timestamp")
            amount_staked = serializer.validated_data.get("amount_staked")
           
            try:
                contestant = Contestants.objects.get(
                    id=contestant_id, game_episode=question.game.game_episode
                )
            except Contestants.DoesNotExist:
                data = {
                    "status": "failed",
                    "message": f"Contestant with id `{contestant_id}` does not exist",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            try:
                contestant_wallet = ContestantsWallets.objects.get(
                    contestant=contestant
                )
            except ContestantsWallets.DoesNotExist:
                data = {
                    "status": "failed",
                    "message": f"Contestant with id `{contestant_id}` does not have a wallet",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            
            question_spend = ContestantQuestionSpend.objects.filter(contestant_id=contestant_id, question_id=question_id).first()
            
            amount = find_amount_key_in_spend_breakdown(question_spend.spend_breakdown, amount_staked)
            
            if amount is None:
                data = {
                    "status": "failed",
                    "message": f"Unable to retrieve amount",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                contestant_answer = ContestsAnswers.objects.create(
                    question_id=question_id,
                    contestant_id=contestant_id,
                    answer=answer.upper(),
                    answered_at=timestamp,
                    amount_staked=amount,
                    # percentage_staked=percentage_staked,
            )
            except IntegrityError as e:
                data = {
                    "status": "failed",
                    "message": f"Contestant with id `{contestant_id}` has already answered this question",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if answer.upper() == question.correct_option:
                contestant_answer.is_correct = True
                contestant_answer.save()
                
            data = {"status": "success", "message": "Answers processed succesfully."}
            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {
                "status": "failed",
                "message": get_serializer_key_error(serializer.errors),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetConstestantAnswers(APIView):

    def post(self, request):

        # game_episode = request.query_params.get("game_episode", None)
        question_id = request.query_params.get("question_id", None)

        if not question_id:
            return Response(
                {"status": "failed", "message": "question_id must be provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            question = Questions.objects.get(id=question_id)
        except Questions.DoesNotExist:
            data = {
                "status": "failed",
                "message": f"Question with id `{question_id}` does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        contestant_answers = []
        answers = ContestsAnswers.objects.filter(question_id=question_id).order_by(
            "answered_at"
        )
        if not answers:
            data = {
                "status": "failed",
                "message": f"Answers for question id `{question_id}` does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        winner_details = None
        if question.won:
            winner_id = question.won_by
            contestant = Contestants.objects.get(id=winner_id)
            winner_details = {
                "contestant_attr": contestant.constestant_attr,
                "contestant_name": contestant.name,
                "contestant_id": contestant.id,
            }
       
        question_details = {
            "question_id": question_id,
            "question_start_time": question.question_start_time,
            "question_winner": question.won_by,
            "is_question_won": question.won
        }
        for ans in answers:
            contestant = Contestants.objects.get(id=ans.contestant_id)
            answer_obj = {
                "contestant": {
                    "contestant_attr": contestant.constestant_attr,
                    "contestant_name": contestant.name,
                    "contestant_id": contestant.id,
                },
                "answer_supplied": ans.answer,
                "is_correct": ans.is_correct,
                "timestamp": ans.answered_at,
                "amount_staked": ans.amount_staked,
            }
            contestant_answers.append(answer_obj)

        data = {
            "status": "success",
            "message": "Question Answers retrieved successfully",
            "data": {"contestant_answers": contestant_answers, "question": question_details, "winner_details": winner_details},
        }
        return Response(data, status=status.HTTP_200_OK)


        
class GetQuestions(GenericViewSet):
    pagination_class = PageNumberPagination

    def get_queryset(self):
        queryset = Questions.objects.all().order_by("-created_at")

        game_id = self.request.query_params.get("game_id")
        asked = self.request.query_params.get("asked")
        won = self.request.query_params.get("won")

        if game_id:
            queryset = queryset.filter(game__id=int(game_id))

        if asked == "false":
            queryset = queryset.filter(asked=False)

        if won == "true":
            queryset = queryset.filter(won=True)

        return queryset

    def list(self, request):
        queryset = self.get_queryset()
        paginator = self.pagination_class()
        paginated_queryset = paginator.paginate_queryset(queryset, request)

        if paginated_queryset is not None:
            serializer = GetQuestionsSerializer(paginated_queryset, many=True)
            return paginator.get_paginated_response(
                {
                    "status": "success",
                    "message": "Questions fetched successfully",
                    "questions": serializer.data,
                }
            )

        # If pagination is not applied (fallback)
        serializer = GetQuestionsSerializer(queryset, many=True)
        return Response(
            data={
                "status": "success",
                "message": "Questions fetched successfully (no pagination)",
                "questions": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def retrieve(self, request, pk: int):
        try:
            question = Questions.objects.get(id=pk)
        except Questions.DoesNotExist:
            data = {"status": "failed", "message": "Question does not exist"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        question_serializer = GetQuestionsSerializer(question)
        return Response(
            data={
                "status": "success",
                "message": "Question fetched successfully",
                "question": question_serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class GetHustleQuestions(APIView):

    def post(self, request, game_episode):

        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_200_OK,
            )

        questions = Questions.objects.filter(game=game)
        if not questions:
            return Response(
                {"status": "failed", "message": "No question found for this game"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        hustle_reveal = HustleReveal.objects.filter(game_episode=game_episode)
        if not hustle_reveal:
            return Response(
                {"status": "failed", "message": "No hustle reveal found for this game"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # if not is_hustle_reveal_completed(game_episode):
        #     return Response(
        #         {"status": "failed", "message": "Hustle Reveal for game not completed"},
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )

        hustle_reveal_questions = []

        try:
            for question in questions:
                contestant = Contestants.objects.get(
                    id=question.hustle_reveal.contestant_id
                )
                question_reveal_dict = {
                    "questions": {
                        "question": question.question,
                        "option_a": question.option_a,
                        "option_b": question.option_b,
                        "option_c": question.option_c,
                        "option_d": question.option_d,
                        "correct_option": question.correct_option,
                        "question_id": question.id,
                        "question_booster": question.question_booster,
                    },
                    "hustle_reveal": {
                        "hustle_name": question.hustle_reveal.hustle_name,
                        "hustle_number": question.hustle_reveal.hustle_number,
                        "hustle_state": question.hustle_reveal.hustle_state,
                        "hustle_amount": float(question.hustle_reveal.hustle_amount),
                    },
                    "contestant": {
                        "contestant_id": question.hustle_reveal.contestant_id,
                        "contestant_attr": contestant.constestant_attr,
                        "contestant_name": contestant.name,
                    },
                }
                hustle_reveal_questions.append(question_reveal_dict)

          
            return Response(
                {
                    "status": "success",
                    "message": "Hustle questions retrieved successfully",
                    "data": {"hustle_questions": hustle_reveal_questions},
                },
                status=status.HTTP_200_OK,
            )
        except Contestants.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Contestant does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class StageOneActivityOneRankingView(APIView):

    def get(self, request):
        game_id = request.query_params.get("game_id")

        if not game_id or not game_id.isdigit():
            return Response(
                {"status": "failed", "message": "Game id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Fetch ranking data from the database or any other source
        ranking_data = SharingPercentageLog.generate_ranking_data(
            game_id=game_id, game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_ONE
        )

        return Response(
            {"status": "success", "data": ranking_data},
            status=status.HTTP_200_OK,
        )


class GetHustlePicksView(APIView):

    def post(self, request, game_episode: int):
        """
        Retrieve all number picks for each contestant in a specific game episode.
        """
        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get all hustle picks for the game episode
        hustle_picks = HustlePicks.objects.filter(game_episode=game_episode)

        if not hustle_picks:
            return Response(
                {
                    "status": "success",
                    "message": "No hustle picks found for this game episode",
                    "data": [],
                },
                status=status.HTTP_200_OK,
            )

        # Prepare the response data
        response_data = []
        for pick in hustle_picks:
            # Handle different formats of number_picks
            if isinstance(pick.number_picks, str):
                try:
                    number_picks_list = eval(pick.number_picks)
                except (SyntaxError, NameError):
                    number_picks_list = []
            else:
                number_picks_list = pick.number_picks or []

            # Create serializer instance for each contestant's picks
            serializer = HustlePicksResponseSerializer(
                {"contestant_id": pick.contestant_id, "picks": number_picks_list}
            )
            response_data.append(serializer.data)

        return Response(
            {
                "status": "success",
                "message": "Hustle picks retrieved successfully",
                "data": response_data,
            },
            status=status.HTTP_200_OK,
        )


class HustlePicksView(APIView):

    serializer_class = PicksSerializer

    def post(self, request, game_episode: int):
        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if game.status != GameStatus.IN_PROGESS:
            return Response(
                {"status": "failed", "message": "Game has not been started."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid(raise_exception=True):
            contestant_id = serializer.validated_data.get("contestant_id")
            number_picks = serializer.validated_data.get("picks")

            try:
                contestant = Contestants.objects.get(
                    id=contestant_id, game_episode=game_episode
                )
                print(f"Contestant: {contestant}")
            except Contestants.DoesNotExist:
                return Response(
                    {"status": "failed", "message": "Contestant does not exist"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            hustle_picks, created = HustlePicks.objects.get_or_create(
                game_episode=game_episode, contestant_id=contestant_id
            )

            if isinstance(hustle_picks.number_picks, str):
                try:
                    number_picks_list = eval(
                        hustle_picks.number_picks
                    )  # Convert string to list if stored as a string
                except (SyntaxError, NameError):
                    number_picks_list = []
            else:
                number_picks_list = hustle_picks.number_picks or []

            if created:
                hustle_picks.number_picks = [number_picks]
            else:
                # Check if the number is already in the list
                if number_picks in number_picks_list:
                    # If already in the list, remove it
                    number_picks_list.remove(number_picks)
                    hustle_picks.number_picks = number_picks_list
                    hustle_picks.save()
                    return Response(
                        {
                            "status": "success",
                            "message": f"Number {number_picks} removed from your picks",
                            "data": number_picks_list,
                        },
                        status=status.HTTP_200_OK,
                    )
                else:
                    # If not in the list, check if we can add it (max 5 picks)
                    if len(number_picks_list) < 5:
                        number_picks_list.append(number_picks)
                    else:
                        return Response(
                            {
                                "status": "failed",
                                "message": "Maximum of 5 picks allowed per contestant",
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                hustle_picks.number_picks = number_picks_list
            hustle_picks.save()
            return Response(
                {"status": "success", "data": "updated successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "failed",
                    "message": f"Invalid data provided -> {serializer.errors}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class GenerateHustleReveal(APIView):
    # HUSTEL PICKS SHOULD HAVE BEEN MARKED AS COMPLETED BEFORE THIS API RUNS
    def post(self, request, game_episode: int):
        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get the constant table for hustle amounts
        try:
            constant_table = ConstantTable.objects.last()
            if not constant_table:
                return Response(
                    {"status": "failed", "message": "Constant table not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            total_amount = constant_table.hustle_activity_one_sharing_amount
        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": f"Error retrieving constant table: {str(e)}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get all hustle picks for the game episode
        hustle_picks = HustlePicks.objects.filter(game_episode=game_episode)

        if not hustle_picks:
            return Response(
                {
                    "status": "failed",
                    "message": "No hustle picks found for this game episode",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if HustleReveal.objects.filter(game_episode=game_episode).exists():
            return Response(
                {
                    "status": "failed",
                    "message": "Hustle Reveals already exist for this game.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Process each contestant's picks
        results = []
        for pick in hustle_picks:
            contestant_id = pick.contestant_id

            # Handle different formats of number_picks
            if isinstance(pick.number_picks, str):
                try:
                    number_picks_list = ast.literal_eval(pick.number_picks)
                except (SyntaxError, NameError):
                    number_picks_list = []
            else:
                number_picks_list = pick.number_picks or []

            # Skip if no picks
            if not number_picks_list:
                continue

            # Ensure we have at most 5 picks
            number_picks_list = number_picks_list[:5]

            # Determine state distribution: 3 duds and 2 opportunities
            states = [HustleStateChoices.DUD] * 3 + [HustleStateChoices.OPPORTUNITY] * 2
            # Shuffle the states to randomize which picks are duds vs opportunities
            random.shuffle(states)

            # Get random business names without repetition
            business_names = list(HustleBusinessChoices.values)
            random.shuffle(business_names)

            # Calculate amount per pick (divide total by number of contestants and then by 5 picks)
            contestant_count = hustle_picks.count()
            base_amount_per_contestant = total_amount / contestant_count
            base_amount_per_pick = base_amount_per_contestant / 5

            # Generate random amounts around the base amount
            # Variation of ±20% for some randomness
            min_amount = base_amount_per_pick * 0.8
            max_amount = base_amount_per_pick * 1.2

            contestant_reveals = []

            # Create HustleReveal objects for each pick
            for i, number in enumerate(number_picks_list):
                # Get a random booster
                booster = random.choice(list(HustleBoosterChoices.values))

                # Generate a random amount within the range
                amount = round(random.uniform(min_amount, max_amount), 2)

                # Create the HustleReveal object
                hustle_reveal = HustleReveal.objects.create(
                    hustle_pick=pick,
                    hustle_name=business_names[i],
                    hustle_number=number,
                    hustle_state=states[i],
                    # hustle_booster=booster,
                    hustle_amount=amount,
                    game_episode=game_episode,
                    contestant_id=contestant_id,
                )

                contestant = Contestants.objects.get(id=contestant_id)
                contestant_wallet = ContestantsWallets.objects.get(
                    contestant=contestant
                )
                contestant_wallet.wallet_balance = (
                    decimal.Decimal(str(amount)) + contestant_wallet.wallet_balance
                )
                contestant_wallet.save()

                if hustle_reveal.hustle_state == HustleStateChoices.OPPORTUNITY:
                    question = Questions.objects.filter(
                        game=game, hustle_reveal__isnull=True
                    ).first()
                    print(f"QUESTION DETAILS >> {question}")
                    question.hustle_reveal = hustle_reveal
                    question.save()
                    # print(f"QUESTION DETAILS >> {question}")
                    # if not question:
                    #     # though this should never happen.
                    #     pass
                    # else:

                contestant_reveals.append(
                    {
                        "id": hustle_reveal.id,
                        "hustle_name": hustle_reveal.hustle_name,
                        "hustle_number": hustle_reveal.hustle_number,
                        "hustle_state": hustle_reveal.hustle_state,
                        # "hustle_booster": hustle_reveal.hustle_booster,
                        "hustle_amount": float(hustle_reveal.hustle_amount),
                    }
                )

            results.append(
                {"contestant_id": contestant_id, "reveals": contestant_reveals}
            )

        return Response(
            {
                "status": "success",
                "message": "Hustle reveals generated successfully",
                "data": results,
            },
            status=status.HTTP_200_OK,
        )


class GetHustleReveal(APIView):
    def post(self, request, game_episode: int):
        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get contestant_id from query params if provided
        contestant_id = request.query_params.get("contestant_id")

        # Filter hustle reveals
        if contestant_id:
            hustle_reveals = HustleReveal.objects.filter(
                game_episode=game_episode, contestant_id=contestant_id
            )
        else:
            hustle_reveals = HustleReveal.objects.filter(game_episode=game_episode)

        if not hustle_reveals:
            return Response(
                {
                    "status": "success",
                    "message": "No hustle reveals found for this game episode",
                    "data": [],
                },
                status=status.HTTP_200_OK,
            )

        # Get all contestants for this game episode
        contestants = {}
        for contestant in AccountsContestants.objects.filter(game_episode=game_episode):
            contestants[contestant.id] = {
                "name": contestant.name,
                "constestant_attr": contestant.constestant_attr,
                "phone_number": contestant.phone_number,
                "final_pot": (
                    float(contestant.final_pot) if contestant.final_pot else 0.0
                ),
                "eliminated_stage": contestant.eliminated_stage,
            }

        # Group by contestant_id
        results = {}
        for reveal in hustle_reveals:
            contestant_id = reveal.contestant_id

            if contestant_id not in results:
                # Initialize with contestant details if available
                contestant_details = contestants.get(contestant_id, {})

                results[contestant_id] = {
                    "contestant_id": contestant_id,
                    "contestant_details": contestant_details,
                    "reveals": [],
                }

            results[contestant_id]["reveals"].append(
                {
                    "id": reveal.id,
                    "hustle_name": reveal.hustle_name,
                    "hustle_number": reveal.hustle_number,
                    "hustle_state": reveal.hustle_state,
                    # "hustle_booster": reveal.hustle_booster,
                    "hustle_amount": float(reveal.hustle_amount),
                }
            )

        return Response(
            {
                "status": "success",
                "message": "Hustle reveals retrieved successfully",
                "data": list(results.values()),
            },
            status=status.HTTP_200_OK,
        )


class StageOneActivityTwoHustlePicksView(APIView):
    serializer_class = PicksSerializer

    def post(self, request):
        game_id = request.query_params.get("game_id")

        if not game_id or not game_id.isdigit():
            return Response(
                {"status": "failed", "message": "Game id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid(raise_exception=True):
            update_number_picks = SharingPercentageLog.update_number_picks(
                game_id=game_id,
                contestant_attr=serializer.validated_data.get("contestant_attr"),
                pick_values=serializer.validated_data.get("picks"),
                game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_TWO,
            )

        return Response(
            {"status": "success", "data": "updated successfully"},
            status=status.HTTP_200_OK,
        )


class BlindHustleInvestmentPercentagesView(APIView):
    serializer_class = PicksSerializer

    def post(self, request):
        game_id = request.query_params.get("game_id")

        if not game_id or not game_id.isdigit():
            return Response(
                {"status": "failed", "message": "Game id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid(raise_exception=True):
            update_number_picks = SharingPercentageLog.update_number_picks(
                game_id=game_id,
                contestant_attr=serializer.validated_data.get("contestant_attr"),
                pick_values=serializer.validated_data.get("picks"),
                game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_TWO,
                blind_hustle=True,
            )

        return Response(
            {"status": "success", "data": "updated successfully"},
            status=status.HTTP_200_OK,
        )


# class StartHustleGameView(APIView):

#     def post(self, request):
#         from game.tasks import run_stage_one_activity_one_setup

#         game_nick = request.data.get("game_nick")

#         if not game_nick:
#             return Response(
#                 {"status": "failed", "message": "Game Nick is required"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         # Call the task to start the game
#         setup_init = run_stage_one_activity_one_setup(game_nick=game_nick)#.apply_async(args=[game_nick], queue="celery_ads_postback")
#         # if run_stage_one_activity_one_setup.get("status") is False:
#         #     return Response(
#         #         run_stage_one_activity_one_setup,
#         #         status=status.HTTP_400_BAD_REQUEST,
#         #     )

#         print("setup init:::::::::::::::")
#         print("setup init:::::::::::::::")
#         print("setup init:::::::::::::::")
#         print(setup_init)
#         print("setup init:::::::::::::::")
#         return Response(
#             setup_init,
#             status=status.HTTP_200_OK,
#         )


class StageOneActivityOneNumberPicksView(APIView):
    serializer_class = PicksSerializer

    def post(self, request):
        game_id = request.query_params.get("game_id")

        if not game_id or not game_id.isdigit():
            return Response(
                {"status": "failed", "message": "Game id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid(raise_exception=True):
            update_number_picks = SharingPercentageLog.update_number_picks(
                game_id=game_id,
                contestant_attr=serializer.validated_data.get("contestant_attr"),
                pick_values=serializer.validated_data.get("picks"),
                game_stage=GameStageChoices.STAGE_ONE_ACTIVITY_ONE,
            )

        return Response(
            {"status": "success", "data": "updated successfully"},
            status=status.HTTP_200_OK,
        )


class AnswerProofHustleQuestions(APIView):
    def post(self, request, question_id: int):
        try:
            question = ProofHustleQuestion.objects.get(id=question_id)
        except ProofHustleQuestion.DoesNotExist:
            data = {
                "status": "failed",
                "message": f"Proof question with id `{question_id}` does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if question.asked:
            data = {"status": "failed", "message": "Proof question already asked"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        serializer = ProofContestantAnswerSerializer(data=request.data)
        if serializer.is_valid():
            answer = serializer.validated_data.get("answer")
            contestant_id = serializer.validated_data.get("contestant_id")
            timestamp = serializer.validated_data.get("timestamp")

            try:
                contestant = Contestants.objects.get(
                    id=contestant_id, game_episode=question.game.game_episode,
                    is_eliminated=False
                )
                
            except Contestants.DoesNotExist:
                data = {
                    "status": "failed",
                    "message": f"Contestant with id `{contestant_id}` does not exist",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            try:
                contestant_answer = ProofHustleQuestionAnswer.objects.create(
                    question_id=question_id,
                    contestant_id=contestant_id,
                    answer=answer.upper(),
                    answered_at=timestamp,
                )
            except IntegrityError:
                data = {
                    "status": "failed",
                    "message": f"Contestant with id `{contestant_id}` has already answered this question",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if answer.upper() == question.correct_option:
                contestant_answer.is_correct = True
                contestant_answer.save()

            # question.asked = True
            # question.save()

            data = {
                "status": "success",
                "message": "Proof question answered successfully.",
            }
            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {
                "status": "failed",
                "message": get_serializer_key_error(serializer.errors),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class GetProofHustleAnswers(APIView):
    def post(self, request):
        # game_episode = request.query_params.get("game_episode", None)
        question_id = request.query_params.get("question_id", None)

        if not question_id:
            return Response(
                {"status": "failed", "message": "question_id must be provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            question = ProofHustleQuestion.objects.get(id=question_id)
        except ProofHustleQuestion.DoesNotExist:
            data = {
                "status": "failed",
                "message": f"Question with id `{question_id}` does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        contestant_answers = []
        answers = ProofHustleQuestionAnswer.objects.filter(question_id=question_id).order_by(
            "answered_at"
        )
        if not answers:
            data = {
                "status": "failed",
                "message": f"Answers for question id `{question_id}` does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        winner_details = None
        if question.won:
            winner_id = question.won_by
            contestant = Contestants.objects.get(id=winner_id)
            winner_details = {
                "contestant_attr": contestant.constestant_attr,
                "contestant_name": contestant.name,
                "contestant_id": contestant.id,
            }
            
        question_details = {
            "question_id": question_id,
            "question_start_time": question.question_start_time,
            "question_winner": question.won_by,
            "is_question_won": question.won
        }
        
        for ans in answers:
            contestant = Contestants.objects.get(id=ans.contestant_id)
            answer_obj = {
                "contestant": {
                    "contestant_attr": contestant.constestant_attr,
                    "contestant_name": contestant.name,
                    "contestant_id": contestant.id,
                },
                "answer_supplied": ans.answer,
                "is_correct": ans.is_correct,
                "timestamp": ans.answered_at
            }
            contestant_answers.append(answer_obj)

        data = {
            "status": "success",
            "message": "Question Answers retrieved successfully",
            "data": {"contestant_answers": contestant_answers, "question": question_details, "winner_details": winner_details}}
        return Response(data, status=status.HTTP_200_OK)


class EndStageTwo(APIView):
    
    def post(self, request, game_episode):
        from django.utils import timezone
        
        logs = logging.getLogger('SOCKET COMMUNICATION')
        
        contestants = Contestants.objects.filter(game_episode=game_episode, is_eliminated=False)

        contestant_wallets = ContestantsWallets.objects.filter(contestant__in=contestants).select_related('contestant')
        
        for contestant_wallet in contestant_wallets:
            contestant_wallet.wallet_balance += contestant_wallet.book_balance
            contestant_wallet.book_balance = 0
            contestant_wallet.save(update_fields=["wallet_balance", "book_balance"])

        sorted_wallets = sorted(contestant_wallets, key=lambda x: x.wallet_balance)

        lowest_two = sorted_wallets[:2]
        eliminated_ids = [w.contestant.id for w in lowest_two]
        print(f"ELIMINATED IDS >> {eliminated_ids}")

        now = timezone.now()
        game = Games.objects.get(game_episode=game_episode)
        game.stage = GameStageChoices.STAGE_THREE
        game.save()
        # Update StageProgress for all contestants of this stage **before modifying Contestant model**
        stage_progress_records = StageProgress.objects.filter(
            game_episode=game_episode,
            stage=StageChoices.PROOF_YOUR_HUSTLE,
            contestant__in=contestants
        ).select_related("contestant")

        # this should be created only for uneliminated contestanrs
        for record in stage_progress_records:
            wallet = next((w for w in contestant_wallets if w.contestant.id == record.contestant.id), None)
            record.pot_at_exit = wallet.wallet_balance if wallet else decimal.Decimal('0.00')
            record.exited_at = now
            record.was_eliminated = record.contestant.id in eliminated_ids
            record.status = "closed"
            record.save(update_fields=["pot_at_exit", "exited_at", "was_eliminated", "status"])
            logs.info(f"Updated StageProgress for {record.contestant.constestant_attr}")

        # Eliminate the lowest two contestants AFTER stage progress update
        for wallet in lowest_two:
            contestant = wallet.contestant
            contestant.is_eliminated = True
            contestant.final_pot = wallet.wallet_balance
            contestant.eliminated_stage = StageChoices.PROOF_YOUR_HUSTLE
            contestant.save()
            logs.info(f"Contestant {contestant.constestant_attr} eliminated in stage_one with wallet: {wallet.wallet_balance}")
        
        
        data = {
            "status": "success",
            "message": "Stage Two Ended Successfully"
        }
        return Response(data, status=status.HTTP_200_OK)

class DebitForProofHustleQuestions(APIView):
    def post(self, request):
        
        serializer = DebitForProofHustleQuestionSerializer(data=request.data)
        if serializer.is_valid():
            contestant_id = serializer.validated_data.get("contestant_id")
            question_id = serializer._validated_data.get("question_id")
            
            try:
                question = ProofHustleQuestion.objects.get(id=question_id)
            except ProofHustleQuestion.DoesNotExist:
                data = {
                    "status": "failed",
                    "message": f"Question with id {question_id} not found.",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                contestant = Contestants.objects.get(id=contestant_id, is_eliminated=False)
            except Contestants.DoesNotExist:
                data = {
                    "status": "failed",
                    "message": f"Contestant with id {contestant_id} not found.",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            wallet = ContestantsWallets.objects.get(contestant=contestant)
            amount = question.allocated_winning_amount

            if wallet.wallet_balance >= amount:
                wallet.wallet_balance -= amount
                wallet.save()
                return Response({
                    "status": "success",
                    "message": f"Wallet debited with {amount} for question {question_id}.",
                    "new_balance": wallet.wallet_balance
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "status": "failed",
                    "message": "Insufficient wallet balance."
                }, status=status.HTTP_400_BAD_REQUEST)

        else:
            data = {
                "status": "failed",
                "message": get_serializer_key_error(serializer.errors),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


# class StartStageTwoApiView(APIView):

#     def post(self, request):
#         from django.utils import timezone
#         game_episode = request.data.get("game_episode")
#         const_table = ConstantTable.objects.last()

#         try:
#             game = Games.objects.get(game_episode=int(game_episode))
#         except Games.DoesNotExist:
#             result = {"status": "failed", "message": f"Game does not exist"}

#             return Response(result, status=status.HTTP_400_BAD_REQUEST)

#         # Check that questions exist for game
#         try:
#             proof_hustle_questions = ProofHustleQuestion.objects.filter(game=game)
#             proof_hustle_questions_count = proof_hustle_questions.count()
#             expected_hustle_questions = const_table.hustle_proof_questions_count
#             sharing_amount = const_table.hustle_proof_sharing_amount

#             if proof_hustle_questions_count < expected_hustle_questions:
#                 result = {
#                     "status": "failed",
#                     "message": f"You need {expected_hustle_questions - proof_hustle_questions_count} more questions to begin this stage",
#                 }
#                 return Response(result, status=status.HTTP_400_BAD_REQUEST)
#             else:
#                 # splt winning amount randomly amongs questions
#                 ProofHustleQuestion.split_winning_amount(
#                     hustle_questions_qs=proof_hustle_questions,
#                     sharing_amount=sharing_amount,
#                     count=proof_hustle_questions_count,
#                 )

#                 result = {
#                     "status": "success",
#                     "message": "Hustle proof stage started successfully",
#                 }
#                 return Response(result, status=status.HTTP_200_OK)
#         except Exception as e:
#             result = {"status": "failed", "message": str(e)}
            
#         contestants = Contestants.objects.filter(game_episode=game_episode, is_eliminated=False)
#         for contestant in contestants:
#             wallet = ContestantsWallets.objects.filter(contestant=contestant).first()
#             try:
#                 StageProgress.objects.create(
#                     game_episode=game.game_episode,
#                     stage=StageChoices.PROOF_YOUR_HUSTLE,  
#                     contestant=contestant,
#                     entered_at=timezone.now(),
#                     pot_at_entry=wallet.wallet_balance
#                 )
#             except IntegrityError:
#                 result = {"status": "failed", "message": str(e)}


class HandleWinnersFundingView(APIView):

    def post(self, request):
        """
        Credit contestant upon answering a question correctly and on time.
        """
        data = request.data
        question_id = data.get("question_id")
        giver_contestant_id = data.get("giver_contestant_id")
        credit_source = data.get("credit_source")
        
        print("INCOMING PAYLOAD:::::::::::::::::::::::::")
        print("INCOMING PAYLOAD:::::::::::::::::::::::::")
        print(request.data)
        print("INCOMING PAYLOAD:::::::::::::::::::::::::")
        print("INCOMING PAYLOAD:::::::::::::::::::::::::")

        result = {}

        giver_contestant: Contestants = None
        giver_wallet = None
        if credit_source == "gameshow_float":
            giver_wallet = ContestantsWallets.get_gameshow_wallet()
        else:
            try:
                giver_contestant = Contestants.objects.get(id=int(giver_contestant_id))
            except Exception as e:
                result["giver_contestant_id"] = (
                    f"HUSTLE PROOF: giver contestant with id {giver_contestant_id} does not exist"
                )
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                giver_wallet = ContestantsWallets.objects.get(contestant=giver_contestant)
            except ContestantsWallets.DoesNotExist:
                result["contestant_wallet"] = (
                    f"HUSTLE PROOF: wallet does not exist for contestant with id: {giver_contestant_id}"
                )
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        question: ProofHustleQuestion = None
        if question_id:
            try:
                question = ProofHustleQuestion.objects.get(id=question_id)
            except ProofHustleQuestion.DoesNotExist:
                result["question"] = (
                    f"HUSTLE PROOF: question does not exist for question with id: {question_id}"
                )
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
        else:
            result["question"] = f"HUSTLE PROOF: question id must be provided"
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        if giver_wallet and question:
            winner_contestant_id = question.won_by

            winner_contestant = None
            try:
                winner_contestant = Contestants.objects.get(id=winner_contestant_id)
            except Contestants.DoesNotExist:
                result["winner_contestant_id"] = (
                    f"HUSTLE PROOF: Winner contestant with id {winner_contestant_id} does not exist"
                )
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            winner_wallet = None
            if winner_contestant:
                try:
                    winner_wallet = ContestantsWallets.objects.get(
                        contestant=winner_contestant
                    )
                except ContestantsWallets.DoesNotExist:
                    result["winner_wallet"] = (
                        f"HUSTLE PROOF: winner wallet does not exist for contestant with id: {winner_contestant.id}"
                    )
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                result["winner_contestant_wallet"] = (
                    f"HUSTLE PROOF: winner wallet does not exist for provided winner_contestant_id"
                )
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if giver_wallet and winner_wallet:
                # Debit giver wallet
                if credit_source == "gameshow_float":
                    pass
                else:
                    print("GIVER WALLET:::::::::", giver_wallet)
                    print("RECEIVER WALLET::::::::", winner_wallet)
                    debit_giver = giver_wallet.deduct_wallet(
                        amount=question.allocated_winning_amount,
                        to_wallet=winner_wallet,
                        description=WalletTransactionDescription.HUSTLE_PROOF_QUESTION_WINNING,
                    )

                # Fund Winner Wallet
                fund_winner = winner_wallet.fund_wallet(
                    amount=question.allocated_winning_amount,
                    from_wallet=giver_wallet,
                    description=WalletTransactionDescription.HUSTLE_PROOF_QUESTION_WINNING,
                )
                result["funding_result"] = f"HUSTLE PROOF: wallet funding successful."
            else:
                result["funding_result"] = (
                    f"HUSTLE PROOF: wallet funding failed. Wallets not found"
                )
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        else:
            result = result

        return Response(result, status=status.HTTP_200_OK)
