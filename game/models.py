# Create your models here.

from django.db import models
from django.forms import ValidationError
from django.utils import timezone

from accounts.models import Contestants
from admin_controller.models import HustleSeason
from game.enums import (
    GameStageChoices,
    GameStatus,
    HustleBoosterChoices,
    HustleBusinessChoices,
    HustleStateChoices,
    StageChoices,
)
from game.helpers.calculations import divide_percentage_randomly
import random


class Games(models.Model):
    # WILL NEED TO CHANGE REFERENCE TO ID AND NOT EPISODE
    hustle_season = models.ForeignKey(
        HustleSeason, on_delete=models.SET_NULL, null=True, blank=True
    )
    game_episode = models.IntegerField(default=0)
    game_nick = models.CharField(max_length=255, unique=True, null=True, blank=True)
    status = models.CharField(
        max_length=255, default=GameStatus.IN_ACTIVE, choices=GameStatus.choices
    )
    stage = models.CharField(
        max_length=255,
        default=GameStageChoices.STAGE_ONE,
        choices=GameStageChoices.choices,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GAME TABLE"
        verbose_name_plural = "GAMES TABLE"

    def __str__(self):
        return f"Game Episode >> {self.game_episode}"

    def save(self, *args, **kwargs):
        if not self.pk:
            last_game = Games.objects.last()
            # last_game = Games.objects.filter(hustle_season=self.hustle_season).last()
            if last_game:
                self.game_episode = last_game.game_episode + 1
            else:
                self.game_episode = 1

        super(Games, self).save(*args, **kwargs)

    @classmethod
    def create_game(cls, game_nick, game_status):
        if cls.objects.filter(game_nick=game_nick).exists():
            return dict(status="failed", message="Game nick already exists")
        return cls.objects.create(
            game_nick=game_nick,
            status=game_status,
        )


class ContestsAnswers(models.Model):
    question_id = models.IntegerField(null=True, blank=True)
    contestant_id = models.IntegerField(null=True, blank=True)
    answer = models.CharField(
        max_length=1,
        choices=[("A", "A"), ("B", "B"), ("C", "C"), ("D", "D"), ("N", "N")],
        null=True,
        blank=True,
    )
    amount_staked = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    is_correct = models.BooleanField(default=False)
    answered_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Contestant Answer"
        verbose_name_plural = "Contestant Answers"
        constraints = [
            models.UniqueConstraint(
                fields=["question_id", "contestant_id"],
                name="unique_question_contestant",
            )
        ]

    def is_question_asked(self, question_id):
        answers_counts = ContestsAnswers.objects.filter(question_id=question_id).count()
        if answers_counts >= 6:
            return True
        return False

    def __str__(self):
        return f"Answer for {self.contestant_id} on question {self.question_id}"


class HustlePicks(models.Model):
    contestant_id = models.IntegerField(null=True, blank=True)
    game_episode = models.IntegerField(null=True, blank=True)
    number_picks = models.JSONField(default=[], null=True, blank=True)
    # game_stage = models.CharField(max_length=1000, choices=GameStageChoices.choices)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def is_completed(self):
        # return true if all 6 contestants have 5 hustle picks
        # return for only a game
        return True


class HustleReveal(models.Model):

    hustle_pick = models.ForeignKey(
        HustlePicks, on_delete=models.CASCADE, null=True, blank=True
    )
    hustle_name = models.CharField(max_length=100, null=True, blank=True)
    hustle_number = models.IntegerField(null=True, blank=True)
    hustle_state = models.CharField(
        max_length=20,
        choices=HustleStateChoices.choices,
        default=HustleStateChoices.DUD,
        null=True,
        blank=True,
    )
    # hustle_booster = models.CharField(
    #     max_length=10,
    #     choices=HustleBoosterChoices.choices,
    #     default=HustleBoosterChoices.X2,
    #     null=True,
    #     blank=True
    # )
    hustle_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    game_episode = models.IntegerField(null=True, blank=True)
    contestant_id = models.IntegerField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Hustle Reveal {self.id}: Reveal Name{self.hustle_name}"

    def is_hustle_reveal_completed(self, game_episode):
        # return true if all 6 contestants have 5 hustle generate picks
        # return for only a game
        return True

    class Meta:
        verbose_name = "Hustle Reveal"
        verbose_name_plural = "Hustle Reveals"


class Questions(models.Model):
    game = models.ForeignKey(Games, on_delete=models.CASCADE, null=True, blank=True)
    hustle_reveal = models.ForeignKey(
        HustleReveal, on_delete=models.SET_NULL, null=True, blank=True
    )
    question = models.TextField()
    option_a = models.TextField(null=True, blank=True)
    option_b = models.TextField(null=True, blank=True)
    option_c = models.TextField(null=True, blank=True)
    option_d = models.TextField(null=True, blank=True)
    correct_option = models.CharField(
        max_length=1,
        choices=[("A", "A"), ("B", "B"), ("C", "C"), ("D", "D")],
        null=True,
        blank=True,
    )
    amount_won = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    won_by = models.IntegerField(null=True, blank=True, help_text="Contestant ID")
    asked = models.BooleanField(default=False)
    won = models.BooleanField(default=False)
    question_booster = models.CharField(
        max_length=10,
        choices=HustleBoosterChoices.choices,
        default=HustleBoosterChoices.X2,
        null=True,
        blank=True,
    )
    question_start_time = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # Write a clean fields to ensure question must have game ID

    def __str__(self):
        truncated_text = (
            self.question[:15] + "..." if len(self.question) > 15 else self.question
        )
        return f"Question: {self.id} - {truncated_text}"

    def clean(self):
        """
        Ensure that the number of questions for a game does not exceed 12.
        """
        if not self.pk:  # Only check if this is a new question
            if self.game:
                question_count = Questions.objects.filter(game=self.game).count()
                if question_count >= 12:
                    raise ValidationError(
                        f"A game cannot have more than 12 questions. Current count: {question_count}"
                    )

    def save(self, *args, **kwargs):
        """
        Ensure game is provided before saving.
        """
        if not self.game:
            raise ValueError("Game must be provided.")
        if (
            not self.option_a
            or not self.option_b
            or not self.option_c
            or not self.option_d
        ):
            raise ValueError("All options must be provided.")
        if not self.correct_option:
            raise ValueError("Correct option must be provided.")

        # self.clean()
        super(Questions, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "GAME QUESTION"
        verbose_name_plural = "GAME QUESTIONS"


class StageProgress(models.Model):

    contestant = models.ForeignKey(
        Contestants, on_delete=models.CASCADE, related_name="stage_progress"
    )
    stage = models.CharField(
        max_length=100, choices=StageChoices.choices, default=StageChoices.HUSTLE_PICKS
    )
    game_episode = models.IntegerField(null=True, blank=True)
    entered_at = models.DateTimeField(null=True, blank=True)
    exited_at = models.DateTimeField(null=True, blank=True)
    pot_at_entry = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    pot_at_exit = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    was_eliminated = models.BooleanField(default=False)
    status = models.CharField(
        max_length=100, default="open", choices=[("open", "open"), ("closed", "closed")]
    )
    # For each stage filter with game and stage, to see contestants that made it and their corresponding details for that stage

    def __str__(self):
        return f"Contestant: {self.contestant.constestant_attr} - Stage: {self.stage} - Game: {self.game_episode}"

    class Meta:
        verbose_name = "STAGE PROGRESS"
        verbose_name_plural = "STAGE PROGRESS"
        constraints = [
            models.UniqueConstraint(
                fields=["contestant", "stage"], name="unique_contestant_stage"
            )
        ]


class SharingPercentageLog(models.Model):
    contestant_id = models.IntegerField()
    contestant_attr = models.CharField(max_length=100)
    game_episode = models.IntegerField(null=True, blank=True)
    game_stage = models.CharField(max_length=1000, choices=GameStageChoices.choices)
    # game_id = models.IntegerField()
    percentage = models.DecimalField(max_digits=100, decimal_places=2)
    sharing_amount = models.DecimalField(default=0.00, max_digits=100, decimal_places=2)
    earned_pot_amount = models.DecimalField(
        default=0.00, max_digits=100, decimal_places=2
    )
    hustle_picks = models.JSONField(default=[], null=True, blank=True)
    number_picks = models.JSONField(default=[], null=True, blank=True)
    picks_sharing_amount = models.JSONField(default=[], null=True, blank=True)
    hustle_investment_percentages = models.JSONField(default=[], null=True, blank=True)
    hustle_investment_amounts = models.JSONField(default=[], null=True, blank=True)
    hustle_investment_multipliers = models.JSONField(default=[], null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def generate_sharing_percentage(
        cls, game_episode, game_stage, available_sharing_amount
    ):
        """
        Generate sharing percentage for a contestant.
        """

        contestants = Contestants.objects.filter(game_episode=game_episode)
        percentages = divide_percentage_randomly(100, len(contestants))

        total_percentage = 0
        for i, contestant in enumerate(contestants):
            percentage = percentages[i]
            total_percentage += percentage

            cls.objects.create(
                contestant_id=contestant.id,
                contestant_attr=contestant.constestant_attr,
                game_episode=game_episode,
                game_stage=game_stage,
                percentage=percentage,
                sharing_amount=available_sharing_amount,
                earned_pot_amount=available_sharing_amount * (percentage / 100),
            )

    @classmethod
    def generate_number_picks_amounts_values(cls, game_episode, game_stage):

        sharing_qs = SharingPercentageLog.objects.filter(
            game_episode=game_episode, game_stage=game_stage
        )

        for sharing in sharing_qs:
            earned_pot_amount = sharing.earned_pot_amount
            percentages = divide_percentage_randomly(100, 5)

            picks_sharing_amount = [
                (float(earned_pot_amount) * (float(percentage) / 100))
                for percentage in percentages
            ]

            sharing.picks_sharing_amount = picks_sharing_amount
            sharing.save()

    @classmethod
    def generate_ranking_data(cls, game_id, game_stage):

        sharing_qs = SharingPercentageLog.objects.filter(
            game_id=game_id, game_stage=game_stage
        ).values(
            "contestant_id",
            "contestant_attr",
            "game_stage",
            "percentage",
            "earned_pot_amount",
        )
        contestants = []
        for sharing in sharing_qs:
            contestants.append(sharing)
        contestants.sort(key=lambda x: x["earned_pot_amount"], reverse=True)
        return contestants

    @classmethod
    def update_number_picks(
        cls, game_id, contestant_attr, pick_values, game_stage, blind_hustle=False
    ):
        sharing_qs = SharingPercentageLog.objects.filter(
            game_id=game_id,
            contestant_attr=contestant_attr,
            game_stage=game_stage,
        )
        if game_stage == GameStageChoices.STAGE_ONE_ACTIVITY_ONE:
            sharing_qs.update(number_picks=pick_values)
        elif blind_hustle:
            sharing_qs.update(hustle_investment_percentages=pick_values)

            for sharing in sharing_qs:
                earned_pot_amount = sharing.earned_pot_amount
                invested_amount_list = [
                    ((perc / 100) * float(earned_pot_amount))
                    for perc in sharing.hustle_investment_percentages
                ]
                multiplier_list = [
                    (inv_amt + sharing.picks_sharing_amount[i]) / inv_amt
                    for i, inv_amt in enumerate(invested_amount_list)
                ]

                sharing.hustle_investment_amounts = invested_amount_list
                sharing.hustle_investment_multipliers = multiplier_list

                sharing.save(
                    update_fields=[
                        "hustle_investment_amounts",
                        "hustle_investment_multipliers",
                    ]
                )
        else:
            sharing_qs.update(hustle_picks=pick_values)

        return True


class ProofHustleQuestion(models.Model):
    game = models.ForeignKey(Games, on_delete=models.CASCADE, null=True, blank=True)
    question = models.TextField()
    option_a = models.TextField(null=True, blank=True)
    option_b = models.TextField(null=True, blank=True)
    option_c = models.TextField(null=True, blank=True)
    option_d = models.TextField(null=True, blank=True)
    correct_option = models.CharField(
        max_length=1,
        choices=[("A", "A"), ("B", "B"), ("C", "C"), ("D", "D")],
        null=True,
        blank=True,
    )
    # winning_amount = models.DecimalField(
    #     max_digits=10, decimal_places=2, null=True, blank=True
    # )
    allocated_winning_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    won_by = models.IntegerField(null=True, blank=True, help_text="Contestant ID")
    asked = models.BooleanField(default=False)
    won = models.BooleanField(default=False)
    question_start_time = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        truncated_text = (
            self.question[:15] + "..." if len(self.question) > 15 else self.question
        )
        return f"Proof Hustle Question: {self.id} - {truncated_text}"

    def clean(self):
        """
        Ensure that the number of proof hustle questions for a game does not exceed 8.
        """
        from admin_controller.models import ConstantTable

        const_table = ConstantTable.objects.last()
        max_questions_count = const_table.hustle_proof_questions_count

        if not self.pk:  # Only check if this is a new question
            if self.game:
                question_count = ProofHustleQuestion.objects.filter(
                    game=self.game
                ).count()
                if question_count >= max_questions_count:
                    raise ValidationError(
                        f"A game cannot have more than {max_questions_count} proof hustle questions. Current count: {question_count}"
                    )

    def save(self, *args, **kwargs):
        """
        Ensure game is provided before saving.
        """
        if not self.game:
            raise ValueError("Game must be provided.")
        if (
            not self.option_a
            or not self.option_b
            or not self.option_c
            or not self.option_d
        ):
            raise ValueError("All options must be provided.")
        if not self.correct_option:
            raise ValueError("Correct option must be provided.")

        super(ProofHustleQuestion, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "PROOF HUSTLE QUESTION"
        verbose_name_plural = "PROOF HUSTLE QUESTIONS"

    @classmethod
    def split_winning_amount(cls, hustle_questions_qs, sharing_amount, count):
        # Fetch all proof hustle questions for this game
        if not hustle_questions_qs.exists():
            raise ValueError("No hustle questions found to allocate winning amounts.")

        if count <= 0:
            raise ValueError("Count must be greater than zero.")

        remaining_amount = sharing_amount
        allocated_amounts = []

        # Calculate the average amount to allocate per question
        average_amount = sharing_amount // count
        allocated_amounts = []

        # Generate amounts close to the average for all but the last question
        for _ in range(count - 1):
            deviation = random.randint(-average_amount // 10, average_amount // 10)
            amount = max(average_amount + deviation, 10000)
            allocated_amounts.append(amount)
            remaining_amount -= amount

        # Assign the remaining amount to the last question, ensuring it's at least 10000
        allocated_amounts.append(max(remaining_amount, 10000))

        # Shuffle the allocated amounts to ensure randomness
        random.shuffle(allocated_amounts)

        # Assign the allocated amounts to the questions
        for question, amount in zip(hustle_questions_qs, allocated_amounts):
            question.allocated_winning_amount = amount
            question.save(update_fields=["allocated_winning_amount"])
        return True


class ProofHustleQuestionAnswer(models.Model):
    question_id = models.IntegerField(null=True, blank=True)
    contestant_id = models.IntegerField(null=True, blank=True)
    answer = models.CharField(
        max_length=1,
        choices=[("A", "A"), ("B", "B"), ("C", "C"), ("D", "D"), ("N", "N")],
        null=True,
        blank=True,
    )
    is_correct = models.BooleanField(default=False)
    answered_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Proof Hustle Question Answer"
        verbose_name_plural = "Proof Hustle Question Answers"
        constraints = [
            models.UniqueConstraint(
                fields=["question_id", "contestant_id"],
                name="unique_proof_question_contestant",
            )
        ]

    def is_question_asked(self, question_id):
        answers_counts = ProofHustleQuestionAnswer.objects.filter(
            question_id=question_id
        ).count()
        if answers_counts >= 6:
            return True
        return False

    def __str__(self):
        return f"Answer for {self.contestant_id} on proof hustle question {self.question_id}"
