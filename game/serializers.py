import datetime

from rest_framework import serializers

from game.enums import ContestantAtrrChoices

from django.utils.dateparse import parse_datetime

class GetQuestionsSerializer(serializers.Serializer):

    question_id = serializers.SerializerMethodField("get_question_d")
    game_id = serializers.SerializerMethodField("get_game_d")
    question = serializers.SerializerMethodField("get_question")
    option_a = serializers.SerializerMethodField("get_option_a")
    option_b = serializers.SerializerMethodField("get_option_b")
    option_c = serializers.SerializerMethodField("get_option_c")
    option_d = serializers.SerializerMethodField("get_option_d")
    correct_option = serializers.SerializerMethodField("get_correct_option")
    winning_amount = serializers.SerializerMethodField("get_winning_amount")
    asked = serializers.SerializerMethodField("get_asked")
    won = serializers.SerializerMethodField("get_won")

    def get_question_d(self, obj):
        return obj.id

    def get_game_d(self, obj):
        return obj.game.id

    def get_question(self, obj):
        return obj.question

    def get_option_a(self, obj):
        return obj.option_a

    def get_option_b(self, obj):
        return obj.option_b

    def get_option_c(self, obj):
        return obj.option_c

    def get_option_d(self, obj):
        return obj.option_d

    def get_correct_option(self, obj):
        return obj.correct_option

    def get_winning_amount(self, obj):
        return obj.winning_amount

    def get_asked(self, obj):
        asked = obj.asked
        if asked is True:
            return "YES"
        else:
            return "NO"

    def get_won(self, obj):
        asked = obj.asked
        if asked is False:
            return "UNDETERMINED"
        else:
            won = obj.won
            if won is True:
                return "YES"
            else:
                return "NO"


class PicksSerializer(serializers.Serializer):
    contestant_id = serializers.IntegerField()
    picks = serializers.IntegerField()

    def validate(self, attrs):
        number_picks = attrs.get("picks")

        if number_picks < 1 or number_picks > 49:
            raise serializers.ValidationError("number of picks must be between 1 and 49")

        return super().validate(attrs)


class ContestantAnswerSerializer(serializers.Serializer):
 
    contestant_id = serializers.IntegerField(required=True)
    answer = serializers.CharField(required=True)
    timestamp = serializers.CharField(required=True)
    amount_staked = serializers.CharField(required=True)
    # percentage_staked = serializers.IntegerField(required=True)

    def validate(self, attrs):
        contestant_id = attrs.get("contestant_id")
        answer = attrs.get("answer")
        timestamp = attrs.get("timestamp")
        
        if not isinstance(contestant_id, int):
            raise serializers.ValidationError("contestant_id must be an integer")
        
        if contestant_id < 1:
            raise serializers.ValidationError("contestant_id must be greater than 0")
        
        if answer.upper() not in ["A", "B", "C", "D", "N"]:
            raise serializers.ValidationError("Answer must be one of 'A', 'B', 'C', 'D', 'N'.")

        try:
            parsed_time = parse_datetime(timestamp)
            if not parsed_time:
                raise serializers.ValidationError(
                    "Start Time must be a valid ISO 8601 datetime string (e.g. '2025-05-22T13:22:39.598Z')"
                )
        except ValueError:
           raise serializers.ValidationError("Start Time not valid Time string. Should be '2025-05-22T13:22:39.598Z'")
        
        attrs["timestamp"] = timestamp
        return attrs


class HustlePicksResponseSerializer(serializers.Serializer):
    contestant_id = serializers.IntegerField()
    picks = serializers.ListField(child=serializers.IntegerField())
  
    
class QuestionStartTimeSerializer(serializers.Serializer):
    start_time = serializers.CharField(required=True)
    question_id = serializers.IntegerField(required=True)
    question_type = serializers.CharField(required=True)

    def validate(self, attrs):
        question_start_time = attrs.get("start_time")
        question_type = attrs.get("question_type")
        if question_type.lower() not in ["stage_1", "stage_2"]:
            raise serializers.ValidationError("Invalid Question Type")
        
        try:
            parsed_time = parse_datetime(question_start_time)
            if not parsed_time:
                raise serializers.ValidationError(
                    "Start Time must be a valid ISO 8601 datetime string (e.g. '2025-05-22T13:22:39.598Z')"
                )
        except ValueError:
           raise serializers.ValidationError("Start Time not valid Time string. Should be '2025-05-22T13:22:39.598Z'")
        
        attrs["question_type"] = question_type.lower()
        attrs["question_start_time"] = question_start_time
        return attrs
    
class ProofContestantAnswerSerializer(serializers.Serializer):
 
    contestant_id = serializers.IntegerField(required=True)
    answer = serializers.CharField(required=True)
    timestamp = serializers.CharField(required=True)

    def validate(self, attrs):
        contestant_id = attrs.get("contestant_id")
        answer = attrs.get("answer")
        timestamp = attrs.get("timestamp")
        
        if not isinstance(contestant_id, int):
            raise serializers.ValidationError("contestant_id must be an integer")
        
        if contestant_id < 1:
            raise serializers.ValidationError("contestant_id must be greater than 0")
        
        if answer.upper() not in ["A", "B", "C", "D", "N"]:
            raise serializers.ValidationError("Answer must be one of 'A', 'B', 'C', 'D', 'N'.")
        
        try:
            parsed_time = parse_datetime(timestamp)
            if not parsed_time:
                raise serializers.ValidationError(
                    "Start Time must be a valid ISO 8601 datetime string (e.g. '2025-05-22T13:22:39.598Z')"
                )
        except ValueError:
           raise serializers.ValidationError("Start Time not valid Time string. Should be '2025-05-22T13:22:39.598Z'")
        
        attrs["timestamp"] = timestamp
        return attrs
    
    
class DebitForProofHustleQuestionSerializer(serializers.Serializer):
    contestant_id = serializers.IntegerField(required=True)
    question_id = serializers.IntegerField(required=True)