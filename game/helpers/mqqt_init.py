import paho.mqtt.client as mqtt
import ssl
import time

# HiveMQ Cloud credentials
broker = "b99c096efca54d0a886f42994500602f.s1.eu.hivemq.cloud"
port = 8883
username = "Liberty"
password = "Liberty2025"
topic = "test/topic"
is_connected = False


def on_connect(client, userdata, flags, rc):
    global is_connected
    if rc == 0:
        print("Connected successfully.")
        is_connected = True
    else:
        print(f"Failed to connect, return code {rc}")
    # Create an MQTT client
    client = mqtt.Client()
    client.username_pw_set(username, password)
    # Enable TLS
    client.tls_set(tls_version=ssl.PROTOCOL_TLS)
    client.on_connect = on_connect
    # Connect to the broker
    client.connect(broker, port, 6000)
    # Start the client loop in the background
    client.loop_start()
    # Wait for the connection to complete
    print("Connecting to broker...")
    while not is_connected:
        time.sleep(1)  # Wait for connection to be established
    print("You are now connected. Start typing your messages.")
    # Main loop for message input and publishing
    try:
        while True:
            message = input("Enter message to publish: ")
            if message.lower() == "exit":
                break
            client.publish(topic, message)
            print(f"Message '{message}' sent to topic '{topic}'")
    except KeyboardInterrupt:
        print("\nExiting...")
    finally:
        client.loop_stop()
        client.disconnect()
