# Register your models here.
from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
# from import_export.admin import ExportMixin, ImportExportModelAdmin

from .models import (
    ContestsAnswers,
    Games,
    HustlePicks,
    HustleReveal,
    Questions,
    SharingPercentageLog,
    StageProgress,
    ProofHustleQuestion,
    ProofHustleQuestionAnswer,
)


class QuestionResource(resources.ModelResource):
    class Meta:
        model = Questions


class GameResource(resources.ModelResource):
    class Meta:
        model = Games
        # fields = ('id', 'game_episode', 'game_nick', 'stage', 'created_at', 'updated_at')
        # export_order = ('id', 'game_episode', 'game_nick', 'stage', 'created_at', 'updated_at')


class StageProgressResource(resources.ModelResource):
    class Meta:
        model = StageProgress


class SharingPercentageLogResource(resources.ModelResource):
    class Meta:
        model = SharingPercentageLog
        # fields = ('id', 'game_episode', 'game_nick', 'stage', 'created_at', 'updated_at')
        # export_order = ('id', 'game_episode', 'game_nick', 'stage', 'created_at', 'updated_at')


class ContestsAnswersResource(resources.ModelResource):
    class Meta:
        model = ContestsAnswers

class ProofHustleAnswersResource(resources.ModelResource):
    class Meta:
        model = ProofHustleQuestionAnswer

class HustlePicksResource(resources.ModelResource):
    class Meta:
        model = HustlePicks


class HustleRevealResource(resources.ModelResource):
    class Meta:
        model = HustleReveal


class ProofHustleQuestionResource(resources.ModelResource):
    class Meta:
        model = ProofHustleQuestion


class QuestionAdmin(admin.ModelAdmin):
    resource_class = QuestionResource
    list_display = ("id", "question", "amount_won", "question_booster", "asked", "won")
    list_filter = ("asked", "won")
    search_fields = ("question",)


class GameAdmin(admin.ModelAdmin):
    resource_class = GameResource
    list_display = ("game_episode", "game_nick", "stage", "created_at", "updated_at")
    search_fields = ("game_episode", "game_nick", "stage")
    list_filter = ("stage", "created_at")
    date_hierarchy = "created_at"



class SharingPercentageLogAdmin(admin.ModelAdmin):
    resource_class = SharingPercentageLogResource
    search_fields = ("game_id", "contestant_attr", "game_stage")
    list_filter = ("game_stage", "contestant_attr", "game_episode")
    # date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StageProgressAdmin(admin.ModelAdmin):
    resource_class = StageProgressResource
    list_display = ("contestant", "stage", "game_episode", "entered_at", "exited_at", "pot_at_entry", "pot_at_exit", "was_eliminated")
    search_fields = ("contestant__name", "game_episode", "stage")
    list_filter = ("stage", "game_episode", "was_eliminated", "entered_at")
    date_hierarchy = "entered_at"


class ContestsAnswersAdmin(admin.ModelAdmin):
    resource_class = ContestsAnswersResource
    list_display = ("contestant_id", "question_id", "answer", "is_correct", "answered_at", "amount_staked")
    search_fields = ("contestant_id", "question_id", "answer")
    ordering = ["answered_at"]
    list_filter = ["question_id"]  # Add this line to enable filtering by question
    # search_fields = ['contestant__name', 'question__question']


class ProofHustleAnswersAdmin(admin.ModelAdmin):
    resource_class = ProofHustleAnswersResource
    list_display = ("contestant_id", "question_id", "answer", "is_correct", "answered_at")
    search_fields = ("contestant_id", "question_id", "answer")
    ordering = ["answered_at"]
    list_filter = ["question_id"]  # Add this line to e

class HustlePicksAdmin(admin.ModelAdmin):
    resource_class = HustlePicksResource
    list_display = ("contestant_id", "game_episode", "get_number_picks", "created_at", "updated_at")
    list_filter = ("game_episode", "created_at")
    search_fields = ("contestant_id", "game_episode")
    date_hierarchy = "created_at"

    def get_number_picks(self, obj):
        return str(obj.number_picks)
    get_number_picks.short_description = "Number Picks"


class HustleRevealAdmin(admin.ModelAdmin):
    resource_class = HustleRevealResource
    list_display = ("hustle_name", "hustle_number", "hustle_state", "hustle_amount", "contestant_id", "game_episode", "created_at")
    list_filter = ("hustle_state", "game_episode", "created_at")
    search_fields = ("hustle_name", "contestant_id", "game_episode")
    date_hierarchy = "created_at"


class ProofHustleQuestionResourceAdmin(ImportExportModelAdmin):
    resource_class = ProofHustleQuestionResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# Register models with admin site
admin.site.register(ContestsAnswers, ContestsAnswersAdmin)
admin.site.register(StageProgress, StageProgressAdmin)
admin.site.register(Games, GameAdmin)
admin.site.register(Questions, QuestionAdmin)
admin.site.register(SharingPercentageLog, SharingPercentageLogAdmin)
admin.site.register(HustlePicks, HustlePicksAdmin)
admin.site.register(HustleReveal, HustleRevealAdmin)
admin.site.register(ProofHustleQuestion, ProofHustleQuestionResourceAdmin)
admin.site.register(ProofHustleQuestionAnswer, ProofHustleAnswersAdmin)
