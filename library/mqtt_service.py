from contextlib import contextmanager
from typing import Dict, Union
import paho.mqtt.client as mqtt
import json
import ssl
import time
import logging

from django.conf import settings


class MQTTService:
    def __init__(self, keepalive=60):
        self.broker = settings.MQTT_BROKER_URL
        self.port = settings.MQTT_BROKER_PORT
        self.username = settings.MQTT_USERNAME
        self.password = settings.MQTT_PASSWORD
        self.keepalive = keepalive
        self.is_connected = False

        self.client = mqtt.Client(clean_session=True)
        self.client.username_pw_set(self.username, self.password)

        if getattr(settings, "MQTT_USE_TLS", True):
            self.client.tls_set(tls_version=ssl.PROTOCOL_TLS)

        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect

        self.logs = logging.getLogger("MQTT SERVICE")

    def on_connect(self, client, userdata, flags, rc, properties=None):
        if rc == 0:
            self.logs.info("Connected successfully to the broker")
            self.is_connected = True
        else:
            self.logs.error(f"Connection failed with code {rc}")

    def on_message(self, client, userdata, message):
        self.logs.info(
            f"Received message: {message.payload.decode()} on topic {message.topic}"
        )
        try:
            from .socket_service import communication_actions

            communication_actions(message.payload)
        except Exception as e:
            self.logs.error(f"Error processing message: {e}")

    def on_disconnect(self, client, userdata, rc):
        self.is_connected = False
        if rc != 0:
            self.logs.warning(
                f"Unexpected disconnection. Return code: {rc}. Attempting to reconnect..."
            )
            while not self.is_connected:
                try:
                    time.sleep(2)
                    self.client.reconnect()
                except Exception as e:
                    self.logs.error(f"Reconnect failed: {e}")

    def connect(self):
        try:
            self.client.connect(self.broker, self.port, self.keepalive)
            self.logs.info("Successfully connected to broker")
        except Exception as e:
            self.logs.error(f"Could not connect to the broker: {e}")
            raise

    def loop_forever(self):
        self.client.loop_forever()

    def publish(
        self, topic: str, payload: Union[Dict, str], qos: int = 1, retain: bool = False
    ) -> None:
        if not self.is_connected:
            self.logs.warning(
                f"Cannot publish to {topic}: Not connected to MQTT broker"
            )
            return

        if isinstance(payload, dict):
            payload_str = json.dumps(payload)
        else:
            payload_str = str(payload)

        preview = payload_str[:100] + ("..." if len(payload_str) > 100 else "")
        self.logs.debug(f"Publishing to topic {topic}: {preview}")
        self.client.publish(topic, payload_str, qos=qos, retain=retain)

    def subscribe(self, topic: str):
        self.client.subscribe(topic)
        self.logs.info(f"Subscribed to topic: {topic}")

    def start(self):
        if not self.is_connected:
            self.connect()
        self.client.loop_start()
        self.logs.info("MQTT client loop started")

    def stop(self):
        self.client.loop_stop()
        self.client.disconnect()
        self.is_connected = False
        self.logs.info("MQTT client loop stopped and disconnected")

    def loop_start(self):
        self.client.loop_start()

    @contextmanager
    def session(self):
        self.start()
        try:
            yield self
        finally:
            self.stop()


        



# class EMQXHandler:
#     _instance = None
#     _initialized = False
#     def __new__(cls, *args, **kwargs):
#         if cls._instance is None:
#             cls._instance = super(EMQXHandler, cls).__new__(cls)
#         return cls._instance
#     def __init__(
#         self,
#         broker=None,
#         port=None,
#         username=None,
#         password=None,
#         keepalive=60,
#         use_tls=False,
#     ):
#         if EMQXHandler._initialized:
#             return
#         # Load configuration with defaults
#         self.broker = broker or config("MQTT_BROKER", cast=str)
#         self.port = port or config("MQTT_PORT", cast=int)
#         self.username = username or config("MQTT_USER", cast=str)
#         self.password = password or config("MQTT_PASS", cast=str)
#         self.keepalive = keepalive
#         self.use_tls = use_tls
#         self.client_id = self.username
#         self.connected = False
#         self.reconnect_delay = 1  # Initial reconnect delay in seconds
#         self.max_reconnect_delay = 120  # Maximum reconnect delay in seconds
#         # Initialize the client with callback API version
#         self.client = mqtt.Client(
#             mqtt.CallbackAPIVersion.VERSION2, self.client_id)
#         # Set authentication if provided
#         if self.username and self.password:
#             self.client.username_pw_set(self.username, self.password)
#         # Set TLS if enabled
#         if self.use_tls:
#             self.client.tls_set()
#         # Set callback functions
#         self.client.on_connect = self._on_connect
#         self.client.on_disconnect = self._on_disconnect
#         self.client.on_message = self._on_message
#         EMQXHandler._initialized = True
#     def _on_connect(self, client, userdata, flags, rc, properties=None):
#         """Callback for when the client receives a CONNACK response from the broker."""
#         if rc == 0:
#             logger.info("Connected successfully to the MQTT broker")
#             self.connected = True
#             self.reconnect_delay = 1  # Reset reconnect delay on successful connection
#         else:
#             logger.error(f"Connection to MQTT broker failed with code {rc}")
#             self.connected = False
#     def _on_disconnect(self, client, userdata, rc, *args, **kwargs):
#         """Callback for when the client disconnects from the broker."""
#         logger.warning(f"Disconnected from MQTT broker with code {rc}")
#         self.connected = False
#         # If disconnection was unexpected, attempt to reconnect
#         if rc != 0:
#             self._reconnect()
#     def _on_message(self, client, userdata, message, *args, **kwargs):
#         """Callback for when a PUBLISH message is received from the broker."""
#         logger.debug(
#             f"Received message: {message.payload.decode()} on topic {message.topic}")
#     def _reconnect(self):
#         """Attempt to reconnect with exponential backoff."""
#         while not self.connected:
#             try:
#                 logger.info(
#                     f"Attempting to reconnect to MQTT broker in {self.reconnect_delay} seconds...")
#                 time.sleep(self.reconnect_delay)
#                 self.connect()
#                 # If we get here, connection was successful
#                 break
#             except Exception as e:
#                 logger.error(f"Reconnection attempt failed: {e}")
#                 # Exponential backoff with maximum delay
#                 self.reconnect_delay = min(
#                     self.reconnect_delay * 2, self.max_reconnect_delay)
#     def connect(self):
#         """Connect to the MQTT broker."""
#         if self.connected:
#             logger.debug("Already connected to MQTT broker")
#             return True
#         try:
#             logger.info(
#                 f"Connecting to MQTT broker at {self.broker}:{self.port}")
#             self.client.connect(self.broker, self.port, self.keepalive)
#             return True
#         except Exception as e:
#             logger.error(f"Could not connect to the MQTT broker: {e}")
#             self.connected = False
#             return False
#     def subscribe(self, topic, qos=0):
#         """Subscribe to a topic with specified QoS."""
#         if not self.connected and not self.connect():
#             logger.error(f"Cannot subscribe to topic {topic}: not connected")
#             return False
#         result, mid = self.client.subscribe(topic, qos)
#         if result == mqtt.MQTT_ERR_SUCCESS:
#             logger.info(f"Subscribed to topic: {topic} with QoS {qos}")
#             return True
#         else:
#             logger.error(f"Failed to subscribe to topic {topic}: {result}")
#             return False
#     def publish(self, topic, message, qos=0, retain=False):
#         """Publish a message to a topic with specified QoS and retain flag."""
#         if not self.connected and not self.connect():
#             logger.error(f"Cannot publish to topic {topic}: not connected")
#             return False
#         # Convert dict to JSON string if necessary
#         if isinstance(message, dict):
#             message = json.dumps(message)
#         result = self.client.publish(topic, message, qos, retain)
#         if result.rc == mqtt.MQTT_ERR_SUCCESS:
#             logger.info(f"Published message to topic: {topic}")
#             return True
#         else:
#             logger.error(
#                 f"Failed to publish message to topic {topic}: {result.rc}")
#             return False
#     def start(self):
#         """Start the MQTT client loop in a background thread."""
#         if not self.connected:
#             self.connect()
#         self.client.loop_start()
#         logger.info("MQTT client loop started")
#     def stop(self):
#         """Stop the MQTT client loop and disconnect."""
#         self.client.loop_stop()
#         self.client.disconnect()
#         self.connected = False
#         logger.info("MQTT client loop stopped and disconnected")
#     def __del__(self):
#         """Clean up resources when the object is garbage collected."""
#         try:
#             if hasattr(self, 'client') and self.client:
#                 self.stop()
#         except Exception as e:
#             logger.error(f"Error during cleanup: {e}")
#     @contextmanager
#     def session(self):
#         """Context manager for MQTT sessions."""
#         self.start()
#         try:
#             yield self
#         finally:
#             self.stop()








