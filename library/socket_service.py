from game.models import HustlePicks
from .mqtt_service import MQTTService
import json
import logging

from game.socket_comm import (
    generate_hustle_reveals,
    hustle_picks_communication,
    hustle_pick_time_elapsed,
    process_and_store_question_spend,
    process_hustle_question_reesult,
    process_question_results,
    start_game_episode,
    initialize_hustle_proof_stage,
    handle_contestant_funding_and_debit,
    end_stage_one
)

def communication_actions(payload):
    
    json_string = json.loads(payload)
    event = json_string.get("event")
    
    if event == "pick_hustle_number":
        hustle_picks_communication(json_string)
        
    elif event == "game_s1_hustle_pick_time_elapse":
        hustle_pick_time_elapsed(json_string)
        
    elif event == "game_start":
        start_game_episode(json_string)
        
    elif event == "game_s1_questions_prep":
        process_and_store_question_spend(json_string)
        
    elif event == "question_s1_time_elapsed":
        process_question_results(json_string)
        
    elif event == "question_s2_time_elapsed":
        process_hustle_question_reesult(json_string)
        
    elif event == "game_s2_init":
        # Start proof hustle phase
        initialize_hustle_proof_stage(json_string)
    elif event == "contestant_funding_and_debit":
        handle_contestant_funding_and_debit(json_string)
        
    elif event == "game_end_stage_one":
       end_stage_one(json_string)       
        
# class EventDispatcher:
#     def __init__(self):
#         self.event_map = {}

#     def register(self, event_name, handler):
#         self.event_map[event_name] = handler

#     def dispatch(self, payload):
#         try:
#             json_data = json.loads(payload)
#             print(f"DATA >>>> {json_data} | TYPE >>>> {type(json_data)}")
#             event = json_data.get("event")
#             handler = self.event_map.get(event)
#             if handler:
#                 handler(json_data)
#             else:
#                 print(f"No handler found for event: {event}")
#         except json.JSONDecodeError as e:
#             print(f"Invalid JSON: {e}")


