import datetime

from rest_framework import serializers
from accounts.models import Contestants


class AssignContestSerializers(serializers.Serializer):
    game_episode = serializers.IntegerField()
    constestants_attr = serializers.Char<PERSON>ield(max_length=100)
    name = serializers.Char<PERSON>ield(max_length=100)
    phone_number = serializers.Char<PERSON><PERSON>(max_length=100)


# class ContestantDetailSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Contestants
#         fields = ['id', 'constestant_attr', 'name', 'phone_number', 'final_pot', 'eliminated_stage', 'is_eliminated', 'login_code', 'created_at', 'updated_at']


class ContestantDetailSerializer(serializers.ModelSerializer):
    actual_balance = serializers.DecimalField(source='contestant_wallet.wallet_balance', max_digits=10, decimal_places=2, read_only=True)
    book_balance = serializers.DecimalField(source='contestant_wallet.book_balance', max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = Contestants
        fields = [
            'id', 'constestant_attr', 'name', 'phone_number',
            'final_pot', 'eliminated_stage', 'is_eliminated',
            'login_code', 'created_at', 'updated_at',
            'actual_balance', 'book_balance'
        ]