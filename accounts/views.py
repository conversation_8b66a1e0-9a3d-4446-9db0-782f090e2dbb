from django.shortcuts import render

from game.enums import GameStatus
from game.models import Games
from accounts.serializers import AssignContestSerializers, ContestantDetailSerializer
from django.contrib.auth.models import User

# Create your views here.
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.models import Contestants, ContestantsWallets
from game.enums import ContestantAtrrChoices


class AssignContestants(APIView):

    def post(self, request):
        serializer = AssignContestSerializers(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        game_episode = validated_data.get("game_episode")

        try:
            game = Games.objects.get(game_episode=game_episode)
            if game.status != GameStatus.IN_ACTIVE:
                return Response(
                    {"status": "failed", "message": "Cannot update contestants for games completed or in progress."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        constestant_attr = validated_data.get("constestants_attr")
        phone_number = validated_data.get("phone_number")
        name = validated_data.get("name")
        if constestant_attr not in ContestantAtrrChoices.values:
            return Response(
                {"status": "failed", "message": "Invalid Contestant type."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        contestant = Contestants.objects.filter(game_episode=game_episode, constestant_attr=constestant_attr).first()
        print(f"CONTESTANT {contestant}")
        
        if not contestant:
            return Response(
                {"status": "failed", "message": "Error occurred! Contestants not yet generated for game"},
                status=status.HTTP_400_BAD_REQUEST,
            )
            
        if not (contestant.name or contestant.phone_number):
            username = f"{constestant_attr}_{game_episode}_{contestant.id}"
            user = User.objects.create_user(username=username, password='password123')
            login_code = contestant.get_random_code()
            contestant.login_code = login_code
            contestant.user = user
            contestant.constestant_attr = constestant_attr
            contestant.phone_number = phone_number
            contestant.name = name
            contestant.save()

            return Response(
                {"status": "success", "data": {"constestant_attr": contestant.constestant_attr, "name": contestant.name, "login_code": login_code}},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "failed", "message": "Contest attribute already assigned for this game."},
                status=status.HTTP_400_BAD_REQUEST,
            )
       

class ContestantLogin(APIView):
    """
    Login view that authenticates using a login_code and returns JWT tokens and contestant details.
    """

    def post(self, request):
        login_code = request.data.get("login_code")

        if not login_code:
            return Response(
                {"status": "failed", "message": "Login code is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            contestant = Contestants.objects.get(login_code=login_code)
        except Contestants.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Invalid login code"},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        print(f"USER {contestant.user}")
        refresh = RefreshToken.for_user(contestant.user)

        return Response(
            {
                "status": "success",
                "message": "Login successful",
                "tokens": {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                },
                "contestant_details": {
                    "name": contestant.name,
                    "contestant_id": contestant.id,
                    "contestant_attr": contestant.constestant_attr,
                },
                "game": {
                    "game_episode": contestant.game_episode,
                }
            },
            status=status.HTTP_200_OK,
        )


class GameContestantsView(APIView):
    """
    View to retrieve all contestants for a specific game episode.
    """

    def post(self, request, game_episode: int):
        """
        Get all contestants for a specific game episode.
        """
        try:
            # Check if the game exists
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Get all contestants for the game episode
        contestants = Contestants.objects.filter(game_episode=game_episode)

        if not contestants:
            return Response(
                {"status": "success", "message": "No contestants found for this game episode", "data": []},
                status=status.HTTP_200_OK,
            )

        # Serialize the contestants
        serializer = ContestantDetailSerializer(contestants, many=True)

        return Response(
            {
                "status": "success",
                "message": "Contestants retrieved successfully",
                "data": serializer.data,
                "game": {
                    "game_episode": game_episode,
                    "game_nick": game.game_nick,
                    "status": game.status,
                    "stage": game.stage,
                }
            },
            status=status.HTTP_200_OK,
        )
        
class GetContestantBalances(APIView):
  

    def post(self, request, game_episode: int):
        """
        Get all contestants for a specific game episode.
        """
        try:
            # Check if the game exists
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Get all contestants for the game episode
        contestants = Contestants.objects.filter(game_episode=game_episode)
        if not contestants:
            return Response(
                {"status": "success", "message": "No contestants found for this game episode", "data": []},
                status=status.HTTP_200_OK,
            )
        
        contestant_balances = []
        for contestant in contestants:
            contastant_wallet = ContestantsWallets.objects.get(contestant=contestant) 
            contestant_balances.append({
                "contestant_id": contestant.id,
                "contestant_attr": contestant.constestant_attr,
                "contestant_name": contestant.name,
                "actual_balance": contastant_wallet.wallet_balance,
                "book_balance": contastant_wallet.book_balance
            })
            
       
        return Response(
            {
                "status": "success",
                "message": "Contestants retrieved successfully",
                "data": {"balances": contestant_balances},
            },
            status=status.HTTP_200_OK,
        )
