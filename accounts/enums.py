from django.db import models
from django.utils.translation import gettext_lazy as _



class DebitCreditType(models.TextChoices):
    DEBIT = "DEBIT", _("DEBIT")
    CREDIT = "CREDIT", _("CREDIT")


class WalletTransactionDescription(models.TextChoices):
    HUSTLE_PROOF_QUESTION_WINNING = "HUSTLE_PROOF_QUESTION_WINNING", _("HUSTLE_PROOF_QUESTION_WINNING")
    HUSTLE_PICK_WINNING = "HUSTLE_PICK_WINNING", _("HUSTLE_PICK_WINNING")


class WalletTypes(models.TextChoices):
    GAME_SHOW = "GAME_SHOW", _("GAME_SHOW")
    # HUSTLE_PICK_WINNING = "HUSTLE_PICK_WINNING", _("HUSTLE_PICK_WINNING")