from django.contrib import admin
from import_export import resources
# from import_export.admin import ImportExportModelAdmin

from accounts.models import (
    Contestants, ContestantsWallets,
    ContestantQuestionSpend
)

class ContestantsResource(resources.ModelResource):
    class Meta:
        model = Contestants
        # fields = ('id', 'constestant_attr', 'game_id', 'name', 'phone_number', 'final_pot',
        #           'eliminated_stage', 'created_at', 'updated_at')
        # export_order = ('id', 'constestant_attr', 'game_id', 'name', 'phone_number', 'final_pot',
        #                 'eliminated_stage', 'created_at', 'updated_at')

class ContestantWalletsResource(resources.ModelResource):
    class Meta:
        model = ContestantsWallets
        
class ContestantQuestionSpendResource(resources.ModelResource):
    class Meta:
        model = ContestantQuestionSpend

class ContestantsWalletsAdmin(admin.ModelAdmin):
    resource_class = ContestantWalletsResource
    list_display = ("contestant",  "wallet_balance", "created_at", "updated_at")
    search_fields = ("contestant__name",)
    list_filter = ("contestant__game_episode", "created_at")
    date_hierarchy = "created_at"

class ContestantsAdmin(admin.ModelAdmin):
    resource_class = ContestantsResource
    list_display = ("id", "constestant_attr", "game_episode", "name", "phone_number", "final_pot", "eliminated_stage", "created_at", "updated_at")
    search_fields = ("constestant_attr", "game_episode", "name", "phone_number")
    list_filter = ("game_episode", "eliminated_stage", "created_at")
    date_hierarchy = "created_at"


class ContestantQuestionSpendAdmin(admin.ModelAdmin):
    resource_class = ContestantQuestionSpendResource
    list_display = ("game_episode", "contestant_id", "question_id", "spend_breakdown", "created_at")
    search_fields = ("game_episode", "contestant_id", "question_id")
    list_filter = ("game_episode", "contestant_id", "question_id", "created_at")
    date_hierarchy = "created_at"

# Register models with admin site
admin.site.register(Contestants, ContestantsAdmin)
admin.site.register(ContestantsWallets, ContestantsWalletsAdmin)
admin.site.register(ContestantQuestionSpend, ContestantQuestionSpendAdmin)