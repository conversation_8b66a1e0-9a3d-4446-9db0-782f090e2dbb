import random
import string
from django.db import IntegrityError, models
from django.contrib.auth.models import User
from accounts.enums import (
    DebitCreditType,
    WalletTransactionDescription,
    WalletTypes,
)

# from game.models import Questions

# Create your models here.


class BaseModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Contestants(models.Model):
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name="contestant_profile",
        null=True,
        blank=True,
    )
    constestant_attr = models.CharField(
        max_length=500, null=True, blank=True
    )  # like c1, c2, etc
    game_episode = models.IntegerField(null=True, blank=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    final_pot = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    eliminated_stage = models.CharField(max_length=100, null=True, blank=True)
    login_code = models.CharField(max_length=10, null=True, blank=True)
    is_eliminated = models.BooleanField(default=False)
    # activity_one_picks = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"ID: {self.id} - Contestant: {self.constestant_attr} - {self.name}: Episod - {self.game_episode}"

    class Meta:
        verbose_name = "Contestant"
        verbose_name_plural = "Contestants"
        constraints = [
            models.UniqueConstraint(
                fields=["game_episode", "constestant_attr"],
                name="unique_game_contestant_attr",
            )
        ]

    @classmethod
    def create_contestants(cls, game_episode, contestant_names):
        """
        Create contestants for a game episode.
        """

        contestants = []
        for name in contestant_names:
            contestant = cls(constestant_attr=name, game_episode=game_episode)
            contestants.append(contestant)
        cls.objects.bulk_create(contestants)
        return contestants

    def get_random_code(self):
        """
        Generate a unique 6-character alphanumeric login code for this instance's game_id.
        """
        game_episode = self.game_episode
        while True:
            code = "".join(random.choices(string.ascii_uppercase + string.digits, k=6))
            if not self.__class__.objects.filter(
                game_episode=game_episode, login_code=code
            ).exists():
                return code


class ContestantsWallets(models.Model):
    contestant = models.OneToOneField(
        Contestants, on_delete=models.CASCADE, related_name="contestant_wallet"
    )
    wallet_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0) # Add minimum value validator
    book_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    wallet_type = models.CharField(
        max_length=1200, choices=WalletTypes.choices, blank=True, null=True
        )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Contestant Wallet: {self.contestant.constestant_attr} - {self.wallet_balance}"

    class Meta:
        verbose_name = "Contestant Wallet"
        verbose_name_plural = "Contestants Wallets"
        constraints = [models.UniqueConstraint(fields=["contestant"], name="unique_contestant_wallet")]
        
    def save(self, *args, **kwargs):
        from django.db import transaction
        try:
            with transaction.atomic():
                super().save(*args, **kwargs)
        except IntegrityError:
            raise ValueError(f"Wallet already exists for contestant '{self.contestant.constestant_attr}' ID '{self.contestant.id}'")
    
    @classmethod
    def get_contestant_wallet(cls, contestant_id):
        try:
            contestant = cls.objects.get(contestant__id=contestant_id)
        except cls.DoesNotExist:
            contestant = None
        return contestant

    @classmethod
    def get_gameshow_wallet(cls):
        return cls.objects.filter(wallet_type=WalletTypes.GAME_SHOW).last()
    
    # @classmethod
    def fund_wallet(self, amount, from_wallet: None, to_wallet: None):
        # create credit record
        balance_after = self.wallet_balance + amount

        record = DebitCreditRecord.objects.create(
            contestant=self.contestant,
            wallet=self,
            amount=amount,
            transaction_type=DebitCreditType.CREDIT,
            balance_before=self.wallet_balance,
            balance_after=balance_after,
            from_wallet=from_wallet,
            to_wallet=to_wallet,
        )

        self.wallet_balance = balance_after
        self.save(update_fields=["wallet_balance"])

        return True

    def deduct_wallet(self, amount, description, to_wallet):
        # create credit record
        if amount > self.wallet_balance:
            return False

        balance_after = self.wallet_balance - amount

        record = DebitCreditRecord.objects.create(
            contestant=self.contestant,
            wallet=self,
            amount=amount,
            transaction_type=DebitCreditType.DEBIT,
            balance_before=self.wallet_balance,
            balance_after=balance_after,
            description=description,
            to_wallet=to_wallet,
        )

        self.wallet_balance = balance_after
        self.save(update_fields=["wallet_balance"])

        return True

    def fund_wallet(self, amount, description, from_wallet):
        # create credit record
        balance_after = self.wallet_balance + amount

        record = DebitCreditRecord.objects.create(
            contestant=self.contestant,
            wallet=self,
            amount=amount,
            transaction_type=DebitCreditType.CREDIT,
            balance_before=self.wallet_balance,
            balance_after=balance_after,
            description=description,
            from_wallet=from_wallet,
        )

        self.wallet_balance = balance_after
        self.save(update_fields=["wallet_balance"])

        return True


class ContestantQuestionSpend(models.Model):
    game_episode = models.IntegerField(null=True, blank=True)
    question_id = models.IntegerField(null=True, blank=True)
    contestant_id = models.IntegerField(null=True, blank=True)
    spend_breakdown = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Contestant Question Spend"
        verbose_name_plural = "Contestant Question Spends"
        constraints = [
            models.UniqueConstraint(
                fields=["question_id", "contestant_id"],
                name="unique_question_contestant_spend",
            )
        ]

class DebitCreditRecord(BaseModel):
    contestant = models.ForeignKey(
        Contestants, on_delete=models.PROTECT, related_name="contestant_debit_credits"
    )
    wallet = models.ForeignKey(
        ContestantsWallets,
        on_delete=models.PROTECT,
        related_name="wallet_debit_credits",
    )
    from_wallet = models.ForeignKey(
        ContestantsWallets,
        on_delete=models.PROTECT,
        related_name="benefactor_wallets",
        blank=True,
        null=True,
    )
    to_wallet = models.ForeignKey(
        ContestantsWallets,
        on_delete=models.PROTECT,
        related_name="beneficiary_wallets",
        blank=True,
        null=True,
    )
    amount = models.DecimalField(default=0.00, decimal_places=2, max_digits=1000)
    balance_before = models.DecimalField(
        default=0.00, decimal_places=2, max_digits=1000
    )
    balance_after = models.DecimalField(default=0.00, decimal_places=2, max_digits=1000)
    transaction_type = models.CharField(
        max_length=1200, choices=DebitCreditType.choices
    )
    description = models.CharField(
        max_length=1200, choices=WalletTransactionDescription.choices
    )

    # @classmethod
    # def debit_wallet(cls, contestant_id_amount, contestant_id):
