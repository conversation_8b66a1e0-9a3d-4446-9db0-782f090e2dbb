# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
logs/
local_settings.py
db.sqlite3
db.sqlite3-journal
media
static/
collected_static/

# Ignore migrations, but keep the migrations directory
*/migrations/
# !*/migrations/__init__.py

# Virtual Environment
venv/
ENV/
env/
.env
.venv/
pyvenv.cfg
pip-selfcheck.json

# Coverage reports
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
