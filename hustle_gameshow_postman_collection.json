{"info": {"name": "Hustle Gameshow API", "description": "API documentation for the Hustle Gameshow application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef**********", "version": "1.0.0"}, "item": [{"name": "Game API", "description": "Endpoints related to game management", "item": [{"name": "Get Questions", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/game/get_questions?game_id=1&asked=false&won=false", "host": ["{{base_url}}"], "path": ["api", "game", "get_questions"], "query": [{"key": "game_id", "value": "1", "description": "Filter by game ID"}, {"key": "asked", "value": "false", "description": "Filter by whether the question has been asked (true/false)"}, {"key": "won", "value": "false", "description": "Filter by whether the question has been won (true/false)"}]}, "description": "Get a list of questions with optional filters"}, "response": []}, {"name": "Get Question by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/game/retrieve_question/:pk", "host": ["{{base_url}}"], "path": ["api", "game", "retrieve_question", ":pk"], "variable": [{"key": "pk", "value": "1", "description": "ID of the question to retrieve"}]}, "description": "Get details of a specific question by ID"}, "response": []}, {"name": "Answer Question", "request": {"method": "POST", "url": {"raw": "{{base_url}}/api/game/answer_questions/:question_id", "host": ["{{base_url}}"], "path": ["api", "game", "answer_questions", ":question_id"], "variable": [{"key": "question_id", "value": "1", "description": "ID of the question to answer"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"answers_list\": [\n    {\n      \"contestant_id\": 1,\n      \"answer\": \"A\",\n      \"timestamp\": \"2023-01-01:12:00:00\"\n    },\n    {\n      \"contestant_id\": 2,\n      \"answer\": \"B\",\n      \"timestamp\": \"2023-01-01:12:00:01\"\n    },\n    {\n      \"contestant_id\": 3,\n      \"answer\": \"C\",\n      \"timestamp\": \"2023-01-01:12:00:02\"\n    },\n    {\n      \"contestant_id\": 4,\n      \"answer\": \"D\",\n      \"timestamp\": \"2023-01-01:12:00:03\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "description": "Submit answers for a specific question from all contestants"}, "response": []}, {"name": "Get Question Answers", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/game/get_question_answers?question_id=1", "host": ["{{base_url}}"], "path": ["api", "game", "get_question_answers"], "query": [{"key": "question_id", "value": "1", "description": "ID of the question to get answers for"}]}, "description": "Get all contestant answers for a specific question"}, "response": []}, {"name": "Start Hustle Game", "request": {"method": "POST", "url": {"raw": "{{base_url}}/api/game/start_gameshow_hustle/", "host": ["{{base_url}}"], "path": ["api", "game", "start_gameshow_hustle", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"game_nick\": \"Game 1\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Start a new Hustle Game with the given nickname"}, "response": []}, {"name": "Stage One Activity One Ranking", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/game/stage_one_activity_one_ranking/?game_id=1", "host": ["{{base_url}}"], "path": ["api", "game", "stage_one_activity_one_ranking", ""], "query": [{"key": "game_id", "value": "1", "description": "ID of the game to get rankings for"}]}, "description": "Get the ranking data for Stage One Activity One"}, "response": []}, {"name": "Stage One Activity One Number Picks", "request": {"method": "POST", "url": {"raw": "{{base_url}}/api/game/stage_one_activity_one_number_picks/?game_id=1", "host": ["{{base_url}}"], "path": ["api", "game", "stage_one_activity_one_number_picks", ""], "query": [{"key": "game_id", "value": "1", "description": "ID of the game"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contestant_attr\": \"C1\",\n  \"picks\": [1, 2, 3, 4, 5]\n}", "options": {"raw": {"language": "json"}}}, "description": "Submit number picks for Stage One Activity One"}, "response": []}, {"name": "Stage One Activity Two Hustle Picks", "request": {"method": "POST", "url": {"raw": "{{base_url}}/api/game/stage_one_activity_two_hustle_picks/?game_id=1", "host": ["{{base_url}}"], "path": ["api", "game", "stage_one_activity_two_hustle_picks", ""], "query": [{"key": "game_id", "value": "1", "description": "ID of the game"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contestant_attr\": \"C1\",\n  \"picks\": [1, 2, 3, 4, 5]\n}", "options": {"raw": {"language": "json"}}}, "description": "Submit hustle picks for Stage One Activity Two"}, "response": []}, {"name": "Blind Hustle Investment Percentages", "request": {"method": "POST", "url": {"raw": "{{base_url}}/api/game/blind_hustle_investment_percentages/?game_id=1", "host": ["{{base_url}}"], "path": ["api", "game", "blind_hustle_investment_percentages", ""], "query": [{"key": "game_id", "value": "1", "description": "ID of the game"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contestant_attr\": \"C1\",\n  \"picks\": [20, 20, 20, 20, 20]\n}", "options": {"raw": {"language": "json"}}}, "description": "Submit blind hustle investment percentages"}, "response": []}]}, {"name": "Accounts API", "description": "Endpoints related to account management", "item": [{"name": "Assign Contestants", "request": {"method": "POST", "url": {"raw": "{{base_url}}/api/accounts/assign_contestants/", "host": ["{{base_url}}"], "path": ["api", "accounts", "assign_contestants", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"game_episode\": 1,\n  \"constestants_attr\": \"C1\",\n  \"name\": \"<PERSON>\",\n  \"phone_number\": \"+**********\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Assign contestant details to a game"}, "response": []}]}, {"name": "Admin Controller API", "description": "Endpoints related to admin control", "item": [{"name": "Start Hustle Game (Admin)", "request": {"method": "POST", "url": {"raw": "{{base_url}}/api/admin-controller/start_gameshow_hustle/", "host": ["{{base_url}}"], "path": ["api", "admin-controller", "start_gameshow_hustle", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"game_nick\": \"Game 1\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Start a new Hustle Game with the given nickname (admin endpoint)"}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}]}