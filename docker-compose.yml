
services:
  game_show:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hustle_gameshow_api
    # Use different commands based on APP_ENV
    command: >
      sh -c "echo 'Database connection info: ${DB_HOST}:${DB_PORT} ${DB_NAME} ${DB_USER}' &&
             echo 'Attempting to connect to database...' &&
             PGPASSWORD=${DB_PASSWORD} psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -c 'SELECT 1' || echo 'Direct database connection failed' &&
             ./wait-for-db.sh ${DB_HOST} ${DB_PORT} &&
             python manage.py makemigrations --noinput && python manage.py migrate --noinput &&
             if [ "$APP_ENV" = "production" ]; then
               gunicorn hustle_gameshow.wsgi:application --bind 0.0.0.0:8000;
             else
               python manage.py runserver 0.0.0.0:8000;
             fi"
    volumes:
      # Mount the entire directory for auto-reloading in development
      - .:/app
    ports:
      - "8000:8000"
    environment:
      - IN_DOCKER=true
      - APP_ENV=development
      - DB_ENGINE=django.db.backends.postgresql
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=host.docker.internal
      - DB_PORT=${DB_PORT}
      - MQTT_BROKER_URL=${MQTT_BROKER_URL}
      - MQTT_BROKER_PORT=${MQTT_BROKER_PORT}
      - MQTT_USERNAME=${MQTT_USERNAME}
      - MQTT_PASSWORD=${MQTT_PASSWORD}
      - MQTT_TOPIC=${MQTT_TOPIC}
    # Ensure container restarts on failure and when Docker daemon restarts
    restart: always
    # Enable auto-reloading in development mode
    # This is handled by mounting the code directory and using Django's runserver
    # which automatically detects file changes

networks:
  default:
    driver: bridge
