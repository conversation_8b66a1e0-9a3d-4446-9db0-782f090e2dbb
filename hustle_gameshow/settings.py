"""
Django settings for hustle_gameshow project.

Generated by 'django-admin startproject' using Django 4.2.20.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from datetime import timedelta
# Load environment variables from .env file
# load_dotenv()

IN_DOCKER = os.environ.get('IN_DOCKER', 'false').lower() == 'true'
# print(f"IN_DOCKER: {IN_DOCKER}")

if not IN_DOCKER:
    
    from dotenv import load_dotenv
    load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
LOG_DIR = BASE_DIR / 'logs'


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-&vi(90q9(t493^cndmwqel+j_n+p-0cpw7f0q0^q90(+wd+ifl')

# Get environment setting
APP_ENV = os.environ.get('APP_ENV', 'development')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true' if APP_ENV == 'development' else False

ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')
ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    # Django built-in apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'django_extensions',

    # Third-party apps
    'rest_framework',
    'drf_yasg',
    'import_export',

    # Project apps
    'hustle_gameshow.apps.HustleGameshowConfig',  # Main project app
    'game',
    'accounts',
    'admin_controller',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]


# DataFlair #Logging Information
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s",
            'datefmt': "%d/%b/%Y %H:%M:%S"
        },
        'simple': {
        'format': '%(asctime)s OMNI_CHANNEL_PRODUCTION_SERVER OMNI_CHANNEL: %(message)s',
        'datefmt': '%Y-%m-%dT%H:%M:%S',
    },
    },
    'handlers': {
        # 'SysLog': {
        # 'level': 'DEBUG',
        # 'class': 'logging.handlers.SysLogHandler',
        # 'formatter': 'simple',
        # 'address': ('logs.papertrailapp.com', 31177)
        # },
        'game_app_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'game_app.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'mqtt_service_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'mqtt_service.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'socket_communication_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'socket_comm.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'admin_controller_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'admin_controller.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'GAME APP': {
            'handlers': ['console', 'game_app_handler'],
            'level': 'DEBUG',
        },
        'MQTT SERVICE': {
            'handlers': ['console', 'mqtt_service_handler'],
            'level': 'DEBUG',
        },
        'SOCKET COMMUNICATION': {
            'handlers': ['console', 'socket_communication_handler'],
            'level': 'DEBUG',
        },
        'ADMIN CONTROLLER': {
            'handlers': ['console', 'admin_controller_handler'],
            'level': 'DEBUG',
        },
    }
}



ROOT_URLCONF = 'hustle_gameshow.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'hustle_gameshow.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': os.environ.get('DB_ENGINE', 'django.db.backends.postgresql'),
        'NAME': os.environ.get('DB_NAME'),
        'USER': os.environ.get('DB_USER'),
        'PASSWORD': os.environ.get('DB_PASSWORD'),
        'HOST': os.environ.get('DB_HOST'),
        'PORT': os.environ.get('DB_PORT'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'

# Configure static files based on environment
if APP_ENV == 'production':
    STATIC_ROOT = os.path.join(BASE_DIR, 'static')
    # Media files (user uploads)
    MEDIA_URL = 'media/'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
else:
    # In development, use the default static directory
    STATIC_ROOT = os.path.join(BASE_DIR, 'static')
    # For development, we'll use the same directory for media
    MEDIA_URL = 'media/'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    # Other settings...
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'AUTH_HEADER_TYPES': ('Bearer',),
}
# Allow all origins (not recommended for production)
CORS_ALLOW_ALL_ORIGINS = True

# Alternatively, specify allowed origins (recommended for production)
# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:3000",  # Example: React frontend
#     "https://your-production-domain.com",
# ]

# Allow credentials (if needed)
CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_HEADERS = [
    # 'https://8f4d-197-210-28-139.ngrok-free.app',
    'content-type',
    'authorization',
    'x-csrftoken',
]

CELERY_BROKER_URL = 'redis://localhost:6379/0'

# print(f"MQTT BROKER URL >> {os.environ.get('MQTT_BROKER_URL')}")
# print(f"MQTT BROKER PORT >> {os.environ.get('MQTT_BROKER_PORT')}")

# ---- MQTT ----
MQTT_BROKER_URL = os.environ.get('MQTT_BROKER_URL')
MQTT_BROKER_PORT = int(os.environ.get('MQTT_BROKER_PORT'))
MQTT_USERNAME = os.environ.get('MQTT_USERNAME')
MQTT_PASSWORD = os.environ.get('MQTT_PASSWORD')
MQTT_TOPIC = os.environ.get('MQTT_TOPIC')

