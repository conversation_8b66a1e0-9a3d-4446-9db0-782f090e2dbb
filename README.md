# Hustle Gameshow

## Docker Setup

This project uses <PERSON><PERSON> and <PERSON>er Compose for local development and production deployment.

### Prerequisites

- Docker
- Docker Compose

### Environment Variables

Copy the example environment file and update it with your settings:

```bash
cp .env.example .env
```

Edit the `.env` file to set your database connection details and other environment variables. The most important variables to set are:

```
# Environment
APP_ENV=development  # Options: development, production

# Database settings
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=your_database_host
DB_PORT=5432
```

### Development Mode

To run the application in development mode with auto-reloading:

```bash
# Set APP_ENV to development (this is the default)
export APP_ENV=development

# Start the containers
docker-compose up
```

#### Auto-Reloading

In development mode, the application will automatically reload when you make changes to the code. This is handled by:

1. Mounting the entire project directory into the container with `volumes: - .:/app`
2. Using Django's development server (`runserver`), which automatically detects file changes

You don't need to restart the container when you make changes to the code - just save your files and the application will reload automatically.

### Production Mode

To run the application in production mode:

```bash
# Set APP_ENV to production
export APP_ENV=production

# Start the containers
docker-compose up -d
```

### Connecting to an External Database

The Docker setup is configured to connect to your existing external PostgreSQL database. Make sure to set the following environment variables in your `.env` file:

```
DB_ENGINE=django.db.backends.postgresql
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=your_database_host
DB_PORT=your_database_port
```

The application will wait for the database to be available before starting. If the database is not available after 30 attempts (with 2-second intervals), the application will proceed anyway but may not function correctly.

### Troubleshooting Database Connection Issues

If you're experiencing database connection issues when running in Docker, consider the following:

1. **Network Access**: Docker containers run in their own network namespace. Make sure your database host is accessible from the Docker container:
   - If using localhost/127.0.0.1 in your .env file, change it to `host.docker.internal` to access the host machine
   - If using a remote database, ensure the database server allows connections from your Docker container's IP

2. **Environment Variables**: Verify that your .env file contains the correct database connection details:
   ```
   DB_ENGINE=django.db.backends.postgresql
   DB_NAME=your_database_name
   DB_USER=your_database_user
   DB_PASSWORD=your_database_password
   DB_HOST=host.docker.internal  # Use this instead of localhost/127.0.0.1
   DB_PORT=5432
   ```

3. **Firewall Settings**: Check if your firewall is blocking connections from the Docker container to your database

4. **Database Server Configuration**: Ensure your PostgreSQL server is configured to accept connections from external hosts in pg_hba.conf

### Docker Commands

#### Building and Starting the Container

```bash
# Build and start the container
docker-compose up

# Build and start the container in detached mode (background)
docker-compose up -d

# Build the container without starting it
docker-compose build

# Force rebuild the container and start it
docker-compose up --build
```

#### Stopping the Container

```bash
# Stop and remove the container
docker-compose down

# Stop and remove the container, along with volumes
docker-compose down -v
```

#### Other Useful Commands

```bash
# View logs
docker-compose logs -f

# Run migrations
docker-compose exec web python manage.py migrate

# Create a superuser
docker-compose exec web python manage.py createsuperuser

# Collect static files
docker-compose exec web python manage.py collectstatic --noinput

# Check the status of the container
docker-compose ps
```

## API Documentation

The API documentation is available at `/swagger/` when the application is running.

## Postman Collection

A Postman collection is included in the repository. To use it:

1. Import the `hustle_gameshow_postman_collection.json` file into Postman
2. Set up an environment in Postman with the following variables:
   - `base_url`: The base URL of your API (e.g., `http://localhost:8000`)
   - `auth_token`: Your authentication token (if required)
