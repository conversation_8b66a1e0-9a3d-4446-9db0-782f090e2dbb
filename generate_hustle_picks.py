import os
import random
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hustle_gameshow.settings')
django.setup()

from game.models import HustlePicks

# Game episode and contestant IDs
game_episode = 1
contestant_ids = [50, 51, 52, 53, 54, 55]

# Generate and save hustle picks
for contestant_id in contestant_ids:
    # Generate 5 unique random numbers between 1 and 20
    number_picks = random.sample(range(1, 21), 5)
    
    # Create or update HustlePicks
    hustle_picks, created = HustlePicks.objects.get_or_create(
        game_episode=game_episode,
        contestant_id=contestant_id,
        defaults={'number_picks': number_picks}
    )
    
    if not created:
        hustle_picks.number_picks = number_picks
        hustle_picks.save()
    
    print(f"Contestant {contestant_id}: {number_picks}")

print("Hust<PERSON> picks generated successfully!")
