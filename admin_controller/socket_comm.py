import decimal
import logging
import random

from django.db import IntegrityError

from accounts.models import ContestantQuestionSpend, Contestants, ContestantsWallets
from admin_controller.utils import get_highest_spend_key
from game.models import ContestsAnswers, Questions 


logs = logging.getLogger('SOCKET COMMUNICATION')

def hustle_question_time_elapsed(json_data: dict):
        from django.utils import timezone
        """
        Process the results for a given question:
        - Fill in missing answers with 'N'.
        - Mark question as asked.
        - Determine the winner (fastest correct answer, break ties randomly).
        - Update the question.won_by field.
        - Update all contestants' wallet balances with booster or stake deduction.
        """
        
        data = json_data.get("payload")
        question_id = data.get("question_id")
        logs.info("entering HUSTLE QUESTION TIME ELAPSED :::::::::::: ")
         
        try:
            question = Questions.objects.get(id=question_id)
        except Questions.DoesNotExist:
            logs.error("exiting HUSTLE QUESTION TIME ELAPSED: << Question does not exist >> ")
            return
        
        if question.asked:
            logs.error("exiting HUSTLE QUESTION TIME ELAPSED: << Question already asked >> ")
            return

        game = question.game
        contestants_for_episode = Contestants.objects.filter(game_episode=game.game_episode)

        answered_ids = ContestsAnswers.objects.filter(question_id=question.id) \
                                            .values_list('contestant_id', flat=True)
        # 1. For each contestant who has NOT answered, create a default 'N' answer.
        
        contestant_not_answered = contestants_for_episode.exclude(id__in=answered_ids)
        for contestant in contestant_not_answered:
            try:
                ContestsAnswers.objects.create(
                    question_id=question.id,
                    contestant_id=contestant.id,
                    answer="N",
                    amount_staked=decimal.Decimal("0.00"),
                    answered_at=timezone.now(),
                )
            except IntegrityError:  
                logs.error("exiting HUSTLE QUESTION TIME ELAPSED: << Contestant Answer Already Exist >> ")
                pass

        question.asked = True
        question.save()
    
        booster = question.question_booster  
    
        multiplier = {
            "X2": 2,
            "X3": 3,
            "X4": 4,
            "X5": 5,
        }.get(booster, 1)
        print(f"MULTIPLIER >> {multiplier}")
        correct_option = question.correct_option

        answers = list(ContestsAnswers.objects.filter(question_id=question.id))
                                        
        correct_answers = [ans for ans in answers if ans.answer == correct_option]
        print(f"LEN CORRECT ANSWERS >> {len(correct_answers)}")

        # winner = None
        # if correct_answers:
        #     earliest_time = min(ans.answered_at for ans in correct_answers)
        #     tied_earliest = [ans for ans in correct_answers if ans.answered_at == earliest_time]

        #     if len(tied_earliest) > 1:
        #         winning_answer = random.choice(tied_earliest)
        #     else:
        #         winning_answer = tied_earliest[0]
        #     winner = winning_answer.contestant_id

        #     question.won_by = winner
        #     question.save()
            
        for ans in answers:
            contestant_id = ans.contestant_id
            staked = ans.amount_staked
            answer = ans.answer
            contestant = Contestants.objects.get(id=contestant_id)
            question_spend = ContestantQuestionSpend.objects.filter(contestant_id=contestant_id, question_id=question_id).first()
            
            highest_stake = get_highest_spend_key(question_spend.spend_breakdown)
            
            wallet = ContestantsWallets.objects.get(contestant=contestant)
            print(f"WALLET BALANCE >> {wallet.wallet_balance}")
            
            if winner and contestant_id == winner:
                gain = staked * multiplier
                wallet.wallet_balance -= staked
                wallet.book_balance += gain
                question.amount_won = gain
                question.won = True
                question.save()
            elif answer == 'N':
                wallet.wallet_balance -= highest_stake
            else:
                wallet.wallet_balance -= staked

            wallet.wallet_balance = wallet.wallet_balance.quantize(decimal.Decimal('0.00'), rounding=decimal.ROUND_HALF_UP)
            wallet.book_balance = wallet.book_balance.quantize(decimal.Decimal('0.00'), rounding=decimal.ROUND_HALF_UP)
            wallet.save()
            
        
        # CONVERT TO EVENT
        # CALCULATE THE FASTEST WITH THE TIMESTAMPS
        # DONT RETURN HERE JUST PUBLISH RESULTS AND BALANCES ON SUCCESS
        # GET THE WALLET BALANCES AND PUBLISH ALONGSIDE 
       