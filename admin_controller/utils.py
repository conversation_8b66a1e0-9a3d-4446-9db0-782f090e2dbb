import random
import logging
from decimal import Decimal
from accounts.models import Contestants, ContestantsWallets
from game.models import HustleReveal
from .models import ConstantTable  # Adjust import to your constants model


def generate_hustle_amounts(game_episode: int):
    """
    Safely and deterministically generate 30 HustleReveal entries such that:
    - All amounts are in 10K increments (between 10K and 100K)
    - Total sum == max_amount from ConstantTable
    - No failures or overflows
    """
    logs = logging.getLogger("ADMIN CONTROLLER")

    try:
        max_amount = int(ConstantTable.objects.last().hustle_reveal_max_amount)
    except (ConstantTable.DoesNotExist, AttributeError, ValueError):
        raise ValueError("MAX_HUSTLE_REVEAL_AMOUNT not properly configured in ConstantTable")

    unit_total = max_amount // 10000  # Convert amount to 10K units
    count = 30

    if unit_total < count:
        raise ValueError("MAX_HUSTLE_REVEAL_AMOUNT too small to divide into 30 parts of at least 10K each.")

    # Start with 1 unit per item to ensure 30 minimums (30 x 10K = 300K minimum)
    base = [1] * count
    remaining_units = unit_total - count  # Distribute the rest

    # Weighted values to increase variety in final amounts
    weights = [1, 2, 3, 5, 7, 6, 5, 4, 3, 2]  # For values 1 to 10

    while remaining_units > 0:
        idx = random.randint(0, count - 1)
        current = base[idx]
        if current < 10:
            possible_extra = min(10 - current, remaining_units)
            if possible_extra > 0:
                base[idx] += 1
                remaining_units -= 1

    # Convert to actual 10K increments and shuffle for randomness
    final_amounts = [Decimal(units * 10000) for units in base]
    random.shuffle(final_amounts)

    hustle_reveals = [
        HustleReveal(game_episode=game_episode, hustle_amount=amount)
        for amount in final_amounts
    ]

    HustleReveal.objects.bulk_create(hustle_reveals)

    logs.info(
        f"Created 30 HustleReveal entries for game_episode {game_episode} "
        f"with total amount: {sum(final_amounts)}"
    )

def generate_contestants(game_instance):
    contestant_names = ["contestant_1", "contestant_2", "contestant_3", "contestant_4", "contestant_5", "contestant_6"]

    contestants = Contestants.create_contestants(
        game_episode=game_instance.game_episode,
        contestant_names=contestant_names,
    )
    
    for contestant in contestants:
        ContestantsWallets.objects.create(contestant=contestant)
        
        
def test_calc(game_episode):
    """
    Just a test function to calculate the total amount of 
    HustleReveal entries for a given game_episode.
    """
    amounts = HustleReveal.objects.filter(game_episode=game_episode)
    total = sum([amount.hustle_amount for amount in amounts])
    
    print(f"Total amount for game_episode {game_episode}: {total}")