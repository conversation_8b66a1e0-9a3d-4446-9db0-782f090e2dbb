import random
import logging
from decimal import Decimal
from accounts.models import Contestants, ContestantsWallets
from game.models import ContestsAnswers, HustleReveal, ProofHustleQuestion, ProofHustleQuestionAnswer, Questions
from .models import ConstantTable  # Adjust import to your constants model


def generate_hustle_amounts(game_episode: int):
    """
    Safely and deterministically generate 30 HustleReveal entries such that:
    - All amounts are in 10K increments (between 10K and 100K)
    - Total sum == max_amount from ConstantTable
    - No failures or overflows
    """
    logs = logging.getLogger("ADMIN CONTROLLER")

    try:
        max_amount = int(ConstantTable.objects.last().hustle_reveal_max_amount)
    except (ConstantTable.DoesNotExist, AttributeError, ValueError):
        raise ValueError("MAX_HUSTLE_REVEAL_AMOUNT not properly configured in ConstantTable")

    unit_total = max_amount // 10000  # Convert amount to 10K units
    count = 30

    if unit_total < count:
        raise ValueError("MAX_HUSTLE_REVEAL_AMOUNT too small to divide into 30 parts of at least 10K each.")

    # Start with 1 unit per item to ensure 30 minimums (30 x 10K = 300K minimum)
    base = [1] * count
    remaining_units = unit_total - count  # Distribute the rest

    # Weighted values to increase variety in final amounts
    weights = [1, 2, 3, 5, 7, 6, 5, 4, 3, 2]  # For values 1 to 10

    while remaining_units > 0:
        idx = random.randint(0, count - 1)
        current = base[idx]
        if current < 10:
            possible_extra = min(10 - current, remaining_units)
            if possible_extra > 0:
                base[idx] += 1
                remaining_units -= 1

    # Convert to actual 10K increments and shuffle for randomness
    final_amounts = [Decimal(units * 10000) for units in base]
    random.shuffle(final_amounts)

    hustle_reveals = [
        HustleReveal(game_episode=game_episode, hustle_amount=amount)
        for amount in final_amounts
    ]

    HustleReveal.objects.bulk_create(hustle_reveals)

    logs.info(
        f"Created 30 HustleReveal entries for game_episode {game_episode} "
        f"with total amount: {sum(final_amounts)}"
    )

def generate_contestants(game_instance):
    contestant_names = ["contestant_1", "contestant_2", "contestant_3", "contestant_4", "contestant_5", "contestant_6"]

    contestants = Contestants.create_contestants(
        game_episode=game_instance.game_episode,
        contestant_names=contestant_names,
    )

    for contestant in contestants:
        ContestantsWallets.objects.create(contestant=contestant)



def test_calc(game_episode):
    """
    Just a test function to calculate the total amount of
    HustleReveal entries for a given game_episode.
    """
    amounts = HustleReveal.objects.filter(game_episode=game_episode)
    total = sum([amount.hustle_amount for amount in amounts])

    print(f"Total amount for game_episode {game_episode}: {total}")


def find_amount_key_in_spend_breakdown(spend_breakdown: dict, amount) -> Decimal:
    """
    Check if the given amount (as float or Decimal) exists as a key in spend_breakdown.
    Returns the Decimal value of the amount if found, else None.
    """
    if not spend_breakdown:
        print("spend breakdown is empty")
        return None

    print(f"AMOUNNT FROM FUNCTION {amount}")
    amount_str = str(amount)


    if amount_str in spend_breakdown:
        return Decimal(amount_str)
    print(f"Amount {amount_str} not found in spend breakdown")
    return None

def get_highest_spend_key(spend_breakdown: dict) -> Decimal:
    if not spend_breakdown:
        return None
    return max(Decimal(k) for k in spend_breakdown.keys())


def calculate_fastest_answer(
    question: Questions | ProofHustleQuestion,
    answers: list[ContestsAnswers | ProofHustleQuestionAnswer],
):
    """
    Calculate the fastest answer for a given question and return detailed results.

    Args:
        question: The question object (Questions or ProofHustleQuestion)
        answers: List of answer objects (ContestsAnswers or ProofHustleQuestionAnswer)

    Returns:
        List of dictionaries with contestant details, answer time, correctness, and winner status
    """
    import random
    from django.utils import timezone

    if not answers:
        return []

    correct_option = question.correct_option
    question_start_time = question.question_start_time

    # If no question start time, use the earliest answer time as reference
    if not question_start_time:
        question_start_time = min(ans.answered_at for ans in answers if ans.answered_at)

    results = []
    correct_answers = []

    # Process each answer to calculate time taken and correctness
    for ans in answers:
        contestant_id = ans.contestant_id
        answer = ans.answer
        answered_at = ans.answered_at

        # Calculate time taken in seconds (with milliseconds)
        if answered_at and question_start_time:
            time_diff = answered_at - question_start_time
            answered_in = round(time_diff.total_seconds(), 3)
        else:
            answered_in = 0.0

        # Check if answer is correct
        is_correct = (answer == correct_option)

        result = {
            "contestant_id": contestant_id,
            "answered_in": answered_in,
            "is_correct": is_correct,
            "is_winner": False,  # Will be updated later
            "answer": answer,
            "answered_at": answered_at
        }

        results.append(result)

        # Collect correct answers for winner determination
        if is_correct:
            correct_answers.append(result)

    # Determine the winner (fastest correct answer)
    winner_contestant_id = None
    if correct_answers:
        # Sort correct answers by time taken (fastest first)
        correct_answers.sort(key=lambda x: x["answered_in"])

        # Get the fastest time
        fastest_time = correct_answers[0]["answered_in"]

        # Find all answers with the fastest time (handle ties)
        tied_fastest = [ans for ans in correct_answers if ans["answered_in"] == fastest_time]

        # If there's a tie, randomly select winner
        if len(tied_fastest) > 1:
            winner = random.choice(tied_fastest)
        else:
            winner = tied_fastest[0]

        winner_contestant_id = winner["contestant_id"]

        # Update the question with winner information
        question.won_by = winner_contestant_id
        question.save()

    # Update winner status in results
    for result in results:
        if result["contestant_id"] == winner_contestant_id:
            result["is_winner"] = True

    # Sort results by correctness (correct first) and then by time (fastest first)
    results.sort(key=lambda x: (not x["is_correct"], x["answered_in"]))

    # Remove internal fields from final result
    for result in results:
        result.pop("answer", None)
        result.pop("answered_at", None)

    return results