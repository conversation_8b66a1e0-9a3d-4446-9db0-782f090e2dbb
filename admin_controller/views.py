# in progress
import decimal
import logging
import random
import logging
from django.db import IntegrityError
from django.shortcuts import render

# Create your views here.
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.models import ContestantQuestionSpend, Contestants, ContestantsWallets
from admin_controller.models import ConstantTable, <PERSON>stleNames, HustleSeason
from admin_controller.serializers import QuestionStartTimeSerializer, StartHustleProofSerializer
from game.calculations import process_hustle_question_breakdown
from game.enums import GameStageChoices, StageChoices
from game.models import ContestsAnswers, Games, HustlePicks, HustleReveal, ProofHustleQuestion, ProofHustleQuestionAnswer, Questions, StageProgress
from game.utils import get_serializer_key_error
from admin_controller.utils import get_highest_spend_key, find_amount_key_in_spend_breakdown

from .utils import generate_hustle_amounts, generate_contestants

logs = logging.getLogger("ADMIN CONTROLLER")

class CreateGameEpisode(APIView):

    def post(self, request):
        """
        Create a new game episode.

        Args:
            request (_type_): _description_

        Returns:
           Dict: Response
        """
        logs.info("entering CREATE GAME EPISODE :::::::::::: ")
        game_nick = request.data.get("game_nick")
        season_id = request.data.get("season_id")

        if not game_nick or not season_id:
            return Response(
                {"status": "failed", "message": "Game Nick is required and Season ID is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            int(season_id)
        except ValueError:
            return Response(
                {"status": "failed", "message": "Season ID must be an integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            season = HustleSeason.objects.get(id=season_id)
            if not season.is_active_season:
                return Response(
                    {"status": "failed", "message": "Hustle Season must be active before saving."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except HustleSeason.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Hustle Season does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # create all game stages table and make status inactive
            game = Games.objects.create(game_nick=game_nick)
            generate_contestants(game_instance=game)
            generate_hustle_amounts(game_episode=game.game_episode)
            data = {"game_episode": game.game_episode}
            return Response(
                {"status": "success", "message": "Game episode created successfully", "data": data},
                status=status.HTTP_201_CREATED,
            )
        except IntegrityError:
            logs.info("exiting CREATE GAME EPISODE: << Game nick already exists >> ")
            return Response(
                {"status": "failed", "message": "Game nick already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except ValueError as err:
            logs.info(f"exiting CREATE GAME EPISODE: << {err} >> ")
            return Response(
                {"status": "failed", "message": f"{err}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as err:
            logs.info(f"exiting CREATE GAME EPISODE: << {err} >> ")
            return Response(
                {"status": "failed", "message": f"Something went wrong -> {err}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )



class StartHustleProofView(APIView):
    serializer_class = StartHustleProofSerializer

    def post(self, request):
        from game.models import ProofHustleQuestion
        from admin_controller.models import ConstantTable

        const_table = ConstantTable.objects.last()

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid(raise_exception=True):
            game_episode = serializer.validated_data.get("game_episode")

        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {
                "status": "failed",
                "message": f"Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check that questions exist for game
        proof_hustle_questions = ProofHustleQuestion.objects.filter(
            game=game
            )
        proof_hustle_questions_count = proof_hustle_questions.count()
        expected_hustle_questions = const_table.hustle_proof_questions_count
        sharing_amount = const_table.hustle_proof_sharing_amount

        if proof_hustle_questions_count < expected_hustle_questions:
            return Response(
            {
            "status": "failed",
            "message": f"You need {expected_hustle_questions - proof_hustle_questions_count} more questions to begin this stage"},
            status=status.HTTP_400_BAD_REQUEST,
        )
        else:
            # splt winning amount randomly amongs questions
            ProofHustleQuestion.split_winning_amount(
                hustle_questions_qs=proof_hustle_questions,
                sharing_amount=sharing_amount,
                count=proof_hustle_questions_count,
            )
            return Response(
            {"status": "success", "message": "Hustle proof stage started successfully"},
            status=status.HTTP_200_OK,
        )


class RunGameHealthCheck(APIView):

    def get(self, request, game_episode: int):
        """
        Check if a game has all data and actions it needs to run successfully.
        """

        try:
            # Check if the game exists
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

        const_table = ConstantTable.objects.last()

        health_details = {}

        # ------ Check if ConstantTable exists ------#
        if ConstantTable.objects.exists():
            health_details["constant_table"] = {
                "status": "success",
                "message": "ConstantTable has at least one object",
            }
        else:
            health_details["constant_table"] = {
                "status": "failed",
                "message": "ConstantTable has no objects. Please create one",
            }

        if game.status != "IN_ACTIVE":
            health_details["game_status"] = {
                "status": "failed",
                "message": f"Game status is {game.status}, expected IN_ACTIVE",
            }
        else:
            health_details["game_status"] = {
                "status": "success",
                "message": f"Game status is {game.status}",
            }

        # ------ Check if the game has exact number of contestants ------#
        contestants = Contestants.objects.filter(game_episode=game_episode)
        contestants_count = contestants.count()
        if contestants_count == 6:
            health_details["contestants"] = {
                "status": "success",
                "message": f"Game has {contestants_count} contestants",
            }
        else:
            health_details["contestants"] = {
                "status": "failed",
                "message": f"Game has {contestants_count} contestants, expected 6",
            }

        # ------ Check if contestants have wallets ------#
        for contestant in contestants:
            contestant_wallet = ContestantsWallets.objects.filter(contestant=contestant)
            if not contestant_wallet.exists():
                health_details["contestant_wallets"] = {
                    "status": "failed",
                    "message": f"Contestant {contestant.id} has no wallet"
                }
                break
            if contestant_wallet.count() > 1:
                health_details["contestant_wallets"] = {
                    "status": "failed",
                    "message": f"Contestant {contestant.id} has more than one wallet"
                }
                break
            if contestant_wallet.first().wallet_balance != 0:
                health_details["contestant_wallets"] = {
                    "status": "failed",
                    "message": f"Contestant {contestant.id} has non-zero wallet balance"
                }
                break
        health_details["contestant_wallets"] = {
            "status": "success",
            "message": f"All contestants have one wallet each with zero balance"
        }

        # ------ Check if the game has exact number of questions ------#
        questions = Questions.objects.filter(game=game)
        questions_count = questions.count()
        if questions_count == const_table.hustle_reveal_questions_count:
            health_details["questions"] = {
                "status": "success",
                "message": f"Game has {questions_count} questions",
            }
        else:
            health_details["questions"] = {
                "status": "failed",
                "message": f"Game has {questions_count} questions, expected {const_table.hustle_reveal_questions_count}"
            }
        for q in questions:
            if q.hustle_reveal:
                health_details["question_state"] = {
                    "status": "failed",
                    "message": f"Question {q.id} has hustle reveal"
                }

                print(f"ONGOING CHECK >> {health_details}")
                break

            if q.asked:
                health_details["question_state"] = {
                    "status": "failed",
                    "message": f"Question {q.id} has already been asked"
                }
                break
            if q.question_start_time:
                health_details["question_state"] = {
                    "status": "failed",
                    "message": f"Question {q.id} has already been started"
                }
                break

            else:
                health_details["question_state"] = {
                    "status": "success",
                    "message": f"All questions have no hustle reveal"
                }

        # ------ Check if the game has no hustle picks ------#
        hustle_reveal = HustlePicks.objects.filter(game_episode=game_episode)
        if not hustle_reveal.exists():
            health_details["hustle_picks"] = {
                "status": "success",
                "message": f"Game has no hustle picks"
            }
        else:
            health_details["hustle_picks"] = {
                "status": "failed",
                "message": f"Game Already has hustle picks"
            }

        hustle_reveal = HustleReveal.objects.filter(game_episode=game_episode)

        if hustle_reveal.count() == 30:
            health_details["hustle_reveal"] = {
                "status": "success",
                "message": f"Game has {hustle_reveal.count()} hustle reveals."
            }
            for hr in hustle_reveal:
                if hr.hustle_amount is None:
                    health_details["hustle_reveal_state"] = {
                        "status": "failed",
                        "message": f"Hustle reveal {hr.id} has no amount."
                    }
                    break
                elif hr.hustle_pick is not None:
                    health_details["hustle_reveal_state"] = {
                        "status": "failed",
                        "message": f"Hustle reveal {hr.id} has already been picked."
                    }
                    break
        else:
            health_details["hustle_reveal"] = {
                "status": "failed",
                "message": f"Game already has 30 hustle reveals."
            }

        # ------ Check that hustle reveal names have been added and exactly 49 ------#
        hustle_name = HustleNames.objects.all() # when season is implemented, this should be related to seasons
        if hustle_name.count() != const_table.hustle_names_count:
            health_details["hustle_name"] = {
                "status": "failed",
                "message": f"Hustle Name not exactly `{const_table.hustle_names_count}` in count."
            }
        else:
            health_details["hustle_name"] = {
                "status": "success",
                "message": f"Hustle Names are exactly `{const_table.hustle_names_count}` in count."
            }

        # -------Check that stage two (proof questions) has expected number of questions--------#
        proof_hustle_questions = ProofHustleQuestion.objects.filter(game=game)
        proof_questions_count = proof_hustle_questions.count()
        if proof_questions_count == const_table.hustle_proof_questions_count:
            health_details["proof_questions"] = {
                "status": "success",
                "message": f"Proof Hustle has {proof_questions_count} questions",
            }
        else:
            health_details["proof_questions"] = {
                "status": "failed",
                "message": f"Proof Hustle has {proof_questions_count} questions, expected {const_table.hustle_proof_questions_count}"
            }

        for q in proof_hustle_questions:
            if q.asked:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} has already been asked"
                }
                break
            if q.question_start_time:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} has already been started"
                }
                break
            if q.won:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} already has won as true"
                }
                break
            if q.allocated_winning_amount:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} already has allocated amount"
                }
                break

            else:
                health_details["proof_question_state"] = {
                    "status": "success",
                    "message": f"All proof questions are clean"
                }

        total_checks = len(health_details)
        success_count = sum(1 for v in health_details.values() if v["status"] == "success")
        failed_count = total_checks - success_count

        data = {
            "summary": {
                "total_checks": total_checks,
                "success_count": success_count,
                "failed_count": failed_count,
                "ratio": f"{success_count} / {total_checks} passed",
            },
            "check_details": health_details
        }

        return Response(data, status=status.HTTP_200_OK)


class TestReassignQuestions(APIView):
    def post(self, request, game_episode: int):

        question_type = self.request.query_params.get("type")

        if question_type == "hustle":
            selected_questions = Questions.objects.order_by('created_at')[:12]
            game = Games.objects.get(game_episode=game_episode)
            for question in selected_questions:
                question.game = game
                question.hustle_reveal = None
                question.question_start_time = None
                question.asked = False
                question.won = False
                question.won_by = None
                question.amount_won = None
                question.save()
            return Response(
                {"status": "success", "message": "Questions reassigned successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            selected_questions = ProofHustleQuestion.objects.order_by('created_at')[:8]
            game = Games.objects.get(game_episode=game_episode)
            for question in selected_questions:
                question.game = game
                question.allocated_winning_amount = None
                question.asked = False
                question.question_start_time = None
                question.won = False
                question.won_by = None
                question.save()
            return Response(
                {"status": "success", "message": "Questions reassigned successfully"},
                status=status.HTTP_200_OK,
            )



class RequestHustNextRevealQuestion(APIView):

    def post(self, requestion, game_episode):
        """
        Request the next hustle reveal question.

        Args:
            requestion (_type_): _description_
            game_episode (_type_): _description_

        Returns:
            _dict_: Response
        """
        
        logs.info("entering REQUEST NEXT HUSTLE REVEAL QUESTION :::::::::::: ")

        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            logs.error("exiting REQUEST NEXT HUSTLE REVEAL QUESTION: << Game does not exist >> ")
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        questions = Questions.objects.filter(game=game, asked=False)
        if not questions:
            logs.error("exiting REQUEST NEXT HUSTLE REVEAL QUESTION: << No questions found for this game >> ")
            return Response(
                {"status": "failed", "code": "700", "message": "Question Exhausted for this game."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        unasked_questions = questions.count()
        print(f"QUESTIONS COUNT >> {unasked_questions}")
        question = questions.first()

        const_table = ConstantTable.objects.last()
        index = (const_table.hustle_reveal_questions_count - unasked_questions) + 1

        breakdown = process_hustle_question_breakdown(
            game_episode=game_episode,
            question=question,
            question_count=unasked_questions
        )
        print(f"BREAKDOWN {breakdown}")

        if type(breakdown) != list:
            return Response(
                {"status": "failed", "code": "650", "message": breakdown},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            contestant = Contestants.objects.get(
                id=question.hustle_reveal.contestant_id
            )
            question_reveal_dict = {
                "questions": {
                    "question": question.question,
                    "option_a": question.option_a,
                    "option_b": question.option_b,
                    "option_c": question.option_c,
                    "option_d": question.option_d,
                    "correct_option": question.correct_option,
                    "question_id": question.id,
                    "question_booster": question.question_booster,
                },
                "hustle_reveal": {
                    "hustle_name": question.hustle_reveal.hustle_name,
                    "hustle_number": question.hustle_reveal.hustle_number,
                    "hustle_state": question.hustle_reveal.hustle_state,
                    "hustle_amount": float(question.hustle_reveal.hustle_amount),
                },
                "contestant": {
                    "contestant_id": question.hustle_reveal.contestant_id,
                    "contestant_attr": contestant.constestant_attr,
                    "contestant_name": contestant.name,
                },
            }
            logs.info("exiting REQUEST NEXT HUSTLE REVEAL QUESTION: << SUCCESSFUL >> ")
            return Response(
                {
                    "status": "success",
                    "message": "Hustle questions retrieved successfully",
                    "data": {
                        "question": question_reveal_dict,
                        "question_index": index,
                        "spend_breakdown": breakdown
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Contestants.DoesNotExist:
            logs.error("exiting REQUEST NEXT HUSTLE REVEAL QUESTION: << Contestant does not exist >> ")
            return Response(
                {"status": "failed", "code": "600", "message": "Contestant does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class QuestionStartTime(APIView):

    def post(self, request):
        """
        Receives the timestamp a questions started.

        Args:
            None

        Returns:
            _dict_: Response
        """

        logs.info("entering QUESTION START TIME :::::::::::: ")
        serializer = QuestionStartTimeSerializer(data=request.data)
        if not serializer.is_valid():
            data = {
                "status": "failed",
                "message": get_serializer_key_error(serializer.errors),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        question_id = serializer.validated_data.get("question_id")
        question_start_time = serializer.validated_data.get("start_time")
        question_type = serializer.validated_data.get("question_type")
        
        if question_type == "stage_1":
            try:
                question = Questions.objects.get(id=question_id)
            except Questions.DoesNotExist:
                logs.error("exiting QUESTION START TIME: << Question does not exist >> ")
                data = {
                    "status": "failed",
                    "message": f"Question with id `{question_id}` does not exist",
                }
            
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        else:
            try:
                question = ProofHustleQuestion.objects.get(id=question_id)
                logs.error("exiting QUESTION START TIME: << Proof Hustle Question does not exist >> ")
            except ProofHustleQuestion.DoesNotExist:
                data = {
                    "status": "failed",
                    "message": f"Proof Hustle Question with id `{question_id}` does not exist",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if question.asked:
            logs.error("exiting QUESTION START TIME: << Question already asked >> ")
            data = {"status": "failed", "message": "Question already asked"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if question.question_start_time:
            data = {"status": "failed", "message": "Question start time already set"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        question.question_start_time = question_start_time
        question.save()
        
        logs.info("exiting QUESTION START TIME: << SUCCESSFUL >> ")
        data = {
            "status": "success",
            "message": "Question start time updated successfully.",
        }
        return Response(data, status=status.HTTP_200_OK)

        
class EndStageOne(APIView):
    
    def post(self, request, game_episode):
        from django.utils import timezone
        
        logs = logging.getLogger('SOCKET COMMUNICATION')
        
        contestants = Contestants.objects.filter(game_episode=game_episode, is_eliminated=False)

        contestant_wallets = ContestantsWallets.objects.filter(contestant__in=contestants).select_related('contestant')
        
        for contestant_wallet in contestant_wallets:
            contestant_wallet.wallet_balance += contestant_wallet.book_balance
            contestant_wallet.book_balance = 0
            contestant_wallet.save(update_fields=["wallet_balance", "book_balance"])

        sorted_wallets = sorted(contestant_wallets, key=lambda x: x.wallet_balance)

        lowest_two = sorted_wallets[:2]
        eliminated_ids = [w.contestant.id for w in lowest_two]
        print(f"ELIMINATED IDS >> {eliminated_ids}")
       
        now = timezone.now()
        game = Games.objects.get(game_episode=game_episode)
        
        if game.stage == GameStageChoices.STAGE_TWO:
            return 
        
        game.stage = GameStageChoices.STAGE_TWO
        game.save()
        # Update StageProgress for all contestants of this stage **before modifying Contestant model**
        stage_progress_records = StageProgress.objects.filter(
            game_episode=game_episode,
            stage=StageChoices.HUSTLE_PICKS,
            contestant__in=contestants
        ).select_related("contestant")

        
        
        for record in stage_progress_records:
            wallet = next((w for w in contestant_wallets if w.contestant.id == record.contestant.id), None)
            record.pot_at_exit = wallet.wallet_balance if wallet else decimal.Decimal('0.00')
            record.exited_at = now
            record.was_eliminated = record.contestant.id in eliminated_ids
            record.status = "closed"
            record.save(update_fields=["pot_at_exit", "exited_at", "was_eliminated", "status"])
            logs.info(f"Updated StageProgress for {record.contestant.constestant_attr}")

        # Eliminate the lowest two contestants AFTER stage progress update
        for wallet in lowest_two:
            contestant = wallet.contestant
            contestant.is_eliminated = True
            contestant.final_pot = wallet.wallet_balance
            contestant.eliminated_stage = StageChoices.HUSTLE_PICKS
            contestant.save()
            logs.info(f"Contestant {contestant.constestant_attr} eliminated in stage_one with wallet: {wallet.wallet_balance}")
            print("ELIMINATED >>>> ")
        
        
        data = {
            "status": "success",
            "message": "Stage One Ended Successfully"
        }
        return Response(data, status=status.HTTP_200_OK)
    
    
class RequestNextProofHustleQuestions(APIView):

     def post(self, request, game_episode):

        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_200_OK,
            )

        questions = ProofHustleQuestion.objects.filter(game=game, asked=False)
        if not questions:
            return Response(
                {"status": "failed", "message": "Question Exhausted for this game."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        unasked_questions = questions.count()
        print(f"QUESTIONS COUNT >> {unasked_questions}")
        question = questions.first()

        const_table = ConstantTable.objects.last()
        index = (const_table.hustle_proof_questions_count - unasked_questions) + 1

        question_dict = {
            "question": question.question,
            "option_a": question.option_a,
            "option_b": question.option_b,
            "option_c": question.option_c,
            "option_d": question.option_d,
            "correct_option": question.correct_option,
            "question_id": question.id,
            "allocated_winning_amount": question.allocated_winning_amount,
        }

        return Response(
            {
                "status": "success",
                "message": "Proof Hustle questions retrieved successfully",
                "data": {"question": question_dict, "index": index},
            },
            status=status.HTTP_200_OK,
        )
        
class ProofQuestionTimeElapsed(APIView):
    
    def post(self, request, question_id):
        from django.utils import timezone
        """
        Process the results for a given question:
        - Fill in missing answers with 'N'.
        - Mark question as asked.
        - Determine the winner (fastest correct answer, break ties randomly).
        - Update the question.won_by field.
        - Update all contestants' wallet balances with booster or stake deduction.
        """
        logs = logging.getLogger('SOCKET COMMUNICATION')
         
        try:
            question = ProofHustleQuestion.objects.get(id=question_id)
        except ProofHustleQuestion.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        if question.asked:
            return Response(
                {"status": "failed", "message": "Question Already Ended"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        game = question.game
        contestants_for_episode = Contestants.objects.filter(game_episode=game.game_episode, is_eliminated=False)
        print(f"LEN CONTESTANTS >> {len(contestants_for_episode)}")

        answered_ids = ProofHustleQuestionAnswer.objects.filter(question_id=question.id) \
                                            .values_list('contestant_id', flat=True)
        
        contestant_not_answered = contestants_for_episode.exclude(id__in=answered_ids)
        for contestant in contestant_not_answered:
            try:
                ProofHustleQuestionAnswer.objects.create(
                    question_id=question.id,
                    contestant_id=contestant.id,
                    answer="N",
                    answered_at=timezone.now(),
                )
            except IntegrityError:
                pass

        question.asked = True
        question.save()
    
        correct_option = question.correct_option

        answers = list(ProofHustleQuestionAnswer.objects.filter(question_id=question.id))
                                        
        correct_answers = [ans for ans in answers if ans.answer == correct_option]
        print(f"LEN CORRECT ANSWERS >> {len(correct_answers)}")

        winner = None
        if correct_answers:
            earliest_time = min(ans.answered_at for ans in correct_answers)
            tied_earliest = [ans for ans in correct_answers if ans.answered_at == earliest_time]

            if len(tied_earliest) > 1:
                winning_answer = random.choice(tied_earliest)
            else:
                winning_answer = tied_earliest[0]
            winner = winning_answer.contestant_id

            question.won_by = winner
            question.won = True
            question.save()
            
        return Response(
                {"status": "success", "message": "Successfully Ended Question."},
                status=status.HTTP_200_OK,
            )
