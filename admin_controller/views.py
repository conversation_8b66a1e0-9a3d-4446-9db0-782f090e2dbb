# in progress
from django.db import IntegrityError
from django.shortcuts import render

# Create your views here.
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.models import Contestants, ContestantsWallets
from admin_controller.models import ConstantTable, HustleNames, HustleSeason
from admin_controller.serializers import StartHustleProofSerializer
from game.models import Games, HustlePicks, HustleReveal, ProofHustleQuestion, Questions

from .utils import generate_hustle_amounts, generate_contestants

class CreateGameEpisode(APIView):
    
    def post(self, request):
        game_nick = request.data.get("game_nick")
        season_id = request.data.get("season_id")
        
        if not game_nick or not season_id:
            return Response(
                {"status": "failed", "message": "Game Nick is required and Season ID is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        try:
            int(season_id)
        except ValueError:
            return Response(
                {"status": "failed", "message": "Season ID must be an integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        try:
            season = HustleSeason.objects.get(id=season_id)
            if not season.is_active_season:
                return Response(
                    {"status": "failed", "message": "Hustle Season must be active before saving."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except HustleSeason.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Hustle Season does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # create all game stages table and make status inactive
            game = Games.objects.create(game_nick=game_nick)
            generate_contestants(game_instance=game)
            generate_hustle_amounts(game_episode=game.game_episode)
            return Response(
                {"status": "success", "message": "Game episode created successfully"},
                status=status.HTTP_201_CREATED,
            )
        except IntegrityError:
            return Response(
                {"status": "failed", "message": "Game nick already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except ValueError as err:
            return Response(
                {"status": "failed", "message": f"{err}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as err:
            return Response(
                {"status": "failed", "message": f"Something went wrong -> {err}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

# class StartHustleGameView(APIView):
#     # CHANGE THIS TO GENERATE GAME, WHILE START GAME WILL UPDATE THE GAME STATUS TO IN PROGRESS

#     def post(self, request):
#         from game.tasks import run_stage_one_activity_one_setup

#         game_nick = request.data.get("game_nick")

#         if not game_nick:
#             return Response(
#                 {"status": "failed", "message": "Game Nick is required"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         # Call the task to start the game
#         # run_stage_one_activity_one_setup.apply_async(args=[game_nick], queue="celery_ads_postback")
#         run_stage_one_activity_one_setup(game_nick)

#         return Response(
#             {"status": "success", "message": "Game started successfully"},
#             status=status.HTTP_200_OK,
#         )

class StartHustleProofView(APIView):
    serializer_class = StartHustleProofSerializer

    def post(self, request):
        from game.models import ProofHustleQuestion
        from admin_controller.models import ConstantTable

        const_table = ConstantTable.objects.last()

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid(raise_exception=True):
            game_episode = serializer.validated_data.get("game_episode")

        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {
                "status": "failed",
                "message": f"Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check that questions exist for game
        proof_hustle_questions = ProofHustleQuestion.objects.filter(
            game=game
            )
        proof_hustle_questions_count = proof_hustle_questions.count()
        expected_hustle_questions = const_table.hustle_proof_questions_count
        sharing_amount = const_table.hustle_proof_sharing_amount

        if proof_hustle_questions_count < expected_hustle_questions:
            return Response(
            {
            "status": "failed",
            "message": f"You need {expected_hustle_questions - proof_hustle_questions_count} more questions to begin this stage"},
            status=status.HTTP_400_BAD_REQUEST,
        )
        else:
            # splt winning amount randomly amongs questions
            ProofHustleQuestion.split_winning_amount(
                hustle_questions_qs=proof_hustle_questions,
                sharing_amount=sharing_amount,
                count=proof_hustle_questions_count,
            )
            return Response(
            {"status": "success", "message": "Hustle proof stage started successfully"},
            status=status.HTTP_200_OK,
        )


class RunGameHealthCheck(APIView):
    
    def get(self, request, game_episode: int):
        """
        Check if a game has all data and actions it needs to run successfully.
        """
        
        try:
            # Check if the game exists
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )
        
        const_table = ConstantTable.objects.last()
        
        health_details = {}
        
        if game.status != "IN_ACTIVE":
            health_details["game_status"] = {
                "status": "failed",
                "message": f"Game status is {game.status}, expected IN_ACTIVE",
            }
        else:
            health_details["game_status"] = {
                "status": "success",
                "message": f"Game status is {game.status}",
            }
            
        # ------ Check if the game has exact number of contestants ------#
        contestants = Contestants.objects.filter(game_episode=game_episode) 
        contestants_count = contestants.count()
        if contestants_count == 6:
            health_details["contestants"] = {
                "status": "success",
                "message": f"Game has {contestants_count} contestants",
            }
        else:
            health_details["contestants"] = {
                "status": "failed",
                "message": f"Game has {contestants_count} contestants, expected 6",
            }
        
        # ------ Check if contestants have wallets ------#
        for contestant in contestants:
            contestant_wallet = ContestantsWallets.objects.filter(contestant=contestant)
            if not contestant_wallet.exists():
                health_details["contestant_wallets"] = {
                    "status": "failed",
                    "message": f"Contestant {contestant.id} has no wallet"
                }
                break
            if contestant_wallet.count() > 1:
                health_details["contestant_wallets"] = {
                    "status": "failed",
                    "message": f"Contestant {contestant.id} has more than one wallet"
                }
                break 
            if contestant_wallet.first().wallet_balance != 0:
                health_details["contestant_wallets"] = {
                    "status": "failed",
                    "message": f"Contestant {contestant.id} has non-zero wallet balance"
                }
                break
        health_details["contestant_wallets"] = {
            "status": "success",
            "message": f"All contestants have one wallet each with zero balance"
        }
            
        # ------ Check if the game has exact number of questions ------#
        questions = Questions.objects.filter(game=game)
        questions_count = questions.count()
        if questions_count == const_table.hustle_reveal_questions_count:
            health_details["questions"] = {
                "status": "success",
                "message": f"Game has {questions_count} questions",
            }
        else:
            health_details["questions"] = {
                "status": "failed",
                "message": f"Game has {questions_count} questions, expected {const_table.hustle_reveal_questions_count}"
            }
        for q in questions:
            if q.hustle_reveal:
                health_details["question_state"] = {
                    "status": "failed",
                    "message": f"Question {q.id} has hustle reveal"
                }
                
                print(f"ONGOING CHECK >> {health_details}")
                break
            
            if q.asked:
                health_details["question_state"] = {
                    "status": "failed",
                    "message": f"Question {q.id} has already been asked"
                }
                break
            if q.question_start_time:
                health_details["question_state"] = {
                    "status": "failed",
                    "message": f"Question {q.id} has already been started"
                }
                break
            
            else:
                health_details["question_state"] = {
                    "status": "success",
                    "message": f"All questions have no hustle reveal"
                }
        
        # ------ Check if the game has no hustle picks ------# 
        hustle_reveal = HustlePicks.objects.filter(game_episode=game_episode)
        if not hustle_reveal.exists():
            health_details["hustle_picks"] = {
                "status": "success",
                "message": f"Game has no hustle picks"
            }
        else:
            health_details["hustle_picks"] = {
                "status": "failed",
                "message": f"Game Already has hustle picks"
            }
            
        hustle_reveal = HustleReveal.objects.filter(game_episode=game_episode)
        
        if hustle_reveal.count() == 30:
            health_details["hustle_reveal"] = {
                "status": "success",
                "message": f"Game has {hustle_reveal.count()} hustle reveals."
            }
            for hr in hustle_reveal:
                if hr.hustle_amount is None:
                    health_details["hustle_reveal_state"] = {
                        "status": "failed",
                        "message": f"Hustle reveal {hr.id} has no amount."
                    }
                    break
                elif hr.hustle_pick is not None:
                    health_details["hustle_reveal_state"] = {
                        "status": "failed",
                        "message": f"Hustle reveal {hr.id} has already been picked."
                    }
                    break
        else:
            health_details["hustle_reveal"] = {
                "status": "failed",
                "message": f"Game already has 30 hustle reveals."
            }
        
        # ------ Check that hustle reveal names have been added and exactly 49 ------#
        hustle_name = HustleNames.objects.all() # when season is implemented, this should be related to seasons
        if hustle_name.count() != const_table.hustle_names_count:
            health_details["hustle_name"] = {
                "status": "failed",
                "message": f"Hustle Name not exactly `{const_table.hustle_names_count}` in count."
            }
        else:
            health_details["hustle_name"] = {
                "status": "success",
                "message": f"Hustle Names are exactly `{const_table.hustle_names_count}` in count."
            }
        
        # -------Check that stage two (proof questions) has expected number of questions--------#
        proof_hustle_questions = ProofHustleQuestion.objects.filter(game=game)
        proof_questions_count = proof_hustle_questions.count()
        if proof_questions_count == const_table.hustle_proof_questions_count:
            health_details["proof_questions"] = {
                "status": "success",
                "message": f"Proof Hustle has {proof_questions_count} questions",
            }
        else:
            health_details["proof_questions"] = {
                "status": "failed",
                "message": f"Proof Hustle has {proof_questions_count} questions, expected {const_table.hustle_proof_questions_count}"
            }
            
        for q in proof_hustle_questions:
            if q.asked:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} has already been asked"
                }
                break
            if q.question_start_time:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} has already been started"
                }
                break
            if q.won:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} already has won as true"
                }
                break
            if q.allocated_winning_amount:
                health_details["proof_question_state"] = {
                    "status": "failed",
                    "message": f"Proof Question {q.id} already has allocated amount"
                }
                break
            
            else:
                health_details["proof_question_state"] = {
                    "status": "success",
                    "message": f"All proof questions are clean"
                }
        
        total_checks = len(health_details)
        success_count = sum(1 for v in health_details.values() if v["status"] == "success")
        failed_count = total_checks - success_count

        data = {
            "summary": {
                "total_checks": total_checks,
                "success_count": success_count,
                "failed_count": failed_count,
                "ratio": f"{success_count} / {total_checks} passed",
            },
            "check_details": health_details
        }
       
        return Response(data, status=status.HTTP_200_OK)
        
        # Check if the game has contestants
        # Check that contestants have wallets with zero balance
        # check that game has questions and the question are exactly 12
        # Check that game has hustle picks and they are not yet assigned
        # Ensure that game has stage 2 questions
        # Ensure that stage 2 sharing amount is already set
        # Ensure that all stages have been created.
        
class TestReassignQuestions(APIView):
    def post(self, request, game_episode: int):
        
        question_type = self.request.query_params.get("type")
        
        if question_type == "hustle":
            selected_questions = Questions.objects.order_by('created_at')[:12]
            game = Games.objects.get(game_episode=game_episode)
            for question in selected_questions:
                question.game = game
                question.hustle_reveal = None
                question.question_start_time = None
                question.asked = False
                question.won = False
                question.won_by = None
                question.amount_won = None
                question.save()
            return Response(
                {"status": "success", "message": "Questions reassigned successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            selected_questions = ProofHustleQuestion.objects.order_by('created_at')[:8]
            game = Games.objects.get(game_episode=game_episode)
            for question in selected_questions:
                question.game = game
                question.allocated_winning_amount = None
                question.asked = False
                question.question_start_time = None
                question.won = False
                question.won_by = None
                question.save()
            return Response(
                {"status": "success", "message": "Questions reassigned successfully"},
                status=status.HTTP_200_OK,
            )

class CheckConstantTableExists(APIView):
    """
    API endpoint to check that the ConstantTable has at least one object created.
    """
    def get(self, request):
        exists = ConstantTable.objects.exists()
        if exists:
            return Response(
                {"status": "success", "message": "ConstantTable has at least one object."},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "failed", "message": "ConstantTable has no objects. Please create one."},
                status=status.HTTP_404_NOT_FOUND,
            )


class RequestHustNextQuestion(APIView):
    
    def post(self, requestion, game_episode):
        
        try:
            game = Games.objects.get(game_episode=game_episode)
        except Games.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Game does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        questions = Questions.objects.filter(game=game, asked=False)
        if not questions:
            return Response(
                {"status": "failed", "code": "700", "message": "Question Exhausted for this game."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        unasked_questions = questions.count()
        print(f"QUESTIONS COUNT >> {unasked_questions}")
        question = questions.first()
        
        const_table = ConstantTable.objects.last()
        index = (const_table.hustle_reveal_questions_count - unasked_questions) + 1
        
        breakdown = process_hustle_question_breakdown(
            game_episode=game_episode,
            question=question,
            question_count=unasked_questions
        )
        print(f"BREAKDOWN {breakdown}")
        
        if type(breakdown) != list:
            return Response(
                {"status": "failed", "code": "650", "message": breakdown},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        try:
            contestant = Contestants.objects.get(
                id=question.hustle_reveal.contestant_id
            )
            question_reveal_dict = {
                "questions": {
                    "question": question.question,
                    "option_a": question.option_a,
                    "option_b": question.option_b,
                    "option_c": question.option_c,
                    "option_d": question.option_d,
                    "correct_option": question.correct_option,
                    "question_id": question.id,
                    "question_booster": question.question_booster,
                },
                "hustle_reveal": {
                    "hustle_name": question.hustle_reveal.hustle_name,
                    "hustle_number": question.hustle_reveal.hustle_number,
                    "hustle_state": question.hustle_reveal.hustle_state,
                    "hustle_amount": float(question.hustle_reveal.hustle_amount),
                },
                "contestant": {
                    "contestant_id": question.hustle_reveal.contestant_id,
                    "contestant_attr": contestant.constestant_attr,
                    "contestant_name": contestant.name,
                },
            }
            
            return Response(
                {
                    "status": "success",
                    "message": "Hustle questions retrieved successfully",
                    "data": {
                        "question": question_reveal_dict,
                        "question_index": index,
                        "spend_breakdown": breakdown
                    },
                },
                status=status.HTTP_200_OK,
            )
            
        except Contestants.DoesNotExist:
            return Response(
                {"status": "failed", "code": "600", "message": "Contestant does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )
