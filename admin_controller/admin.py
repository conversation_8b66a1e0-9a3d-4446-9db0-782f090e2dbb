from django.contrib import admin
from import_export import resources
from import_export.admin import ExportMixin, ImportExportModelAdmin

from .models import (
    ConstantTable,
    HustleNames,
    HustleSeason
)

# ------------------------------
# Resource classes
# ------------------------------
class ConstantTableResource(resources.ModelResource):
    class Meta:
        model = ConstantTable
        fields = ('id', 'hustle_reveal_max_amount', 'hustle_activity_two_sharing_amount')
        export_order = ('id', 'hustle_activity_one_sharing_amount', 'hustle_activity_two_sharing_amount')
        

class HustleSeasonResource(resources.ModelResource):
    class Meta:
        model = HustleSeason
        fields = ('id', 'season', 'year', 'description', 'is_active_season', 'created_at', 'updated_at')


class HustleNamesResource(resources.ModelResource):
    class Meta:
        model = HustleNames
        fields = ('id', 'name', 'description', 'hustle_season__season', 'created_at', 'updated_at')


# ------------------------------
# Admin classes
# ------------------------------
class ConstantTableAdmin(admin.ModelAdmin):
    resource_class = ConstantTableResource
    list_display = ('id', 'hustle_reveal_max_amount', 'hustle_activity_two_sharing_amount')

    def has_add_permission(self, request):
        # Only allow adding if no records exist
        return ConstantTable.objects.count() == 0

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of constants
        return False
    
class HustleSeasonAdmin(ExportMixin, admin.ModelAdmin):
    resource_class = HustleSeasonResource
    list_display = ('id', 'season', 'year', 'is_active_season', 'created_at', 'updated_at')
    list_filter = ('is_active_season', 'year')
    search_fields = ('season', 'year')
    ordering = ('-created_at',)

class HustleNamesAdmin(ImportExportModelAdmin):
    resource_class = HustleNamesResource
    list_display = ('name', 'hustle_season', 'created_at', 'updated_at')
    list_filter = ('hustle_season',)
    search_fields = ('name',)
    ordering = ('-created_at',)

    # Ensure season is set and active before save
    def save_model(self, request, obj, form, change):
        if not obj.hustle_season:
            raise ValueError("Hustle Season must be set before saving.")
        if not obj.hustle_season.is_active_season:
            raise ValueError("Hustle Season must be active before saving.")
        super().save_model(request, obj, form, change)


# Register models with admin site
admin.site.register(ConstantTable, ConstantTableAdmin)
admin.site.register(HustleSeason, HustleSeasonAdmin)
admin.site.register(HustleNames, HustleNamesAdmin)
