from django.urls import path
from admin_controller import views
from .views import CheckConstantTableExists

urlpatterns = [
    path("create_gameshow_episode/", views.CreateGameEpisode.as_view(), name="admin_start_game_hustle"),
    path("start_hustle_proof/", views.StartHustleProofView.as_view(), name="admin_start_hustle_proof"),
    path("game_health_check/<int:game_episode>", views.RunGameHealthCheck.as_view(), name="game_health_check"),
    # Needed for test only
    path("test_reassign_questions/<int:game_episode>", views.TestReassignQuestions.as_view(), name="test_reassign_questions"),
    path('check-constant-table/', CheckConstantTableExists.as_view(), name='check-constant-table'),
]