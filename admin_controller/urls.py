from django.urls import path
from admin_controller import views

urlpatterns = [
    path("create_gameshow_episode/", views.CreateGameEpisode.as_view(), name="admin_start_game_hustle"),
    path("start_hustle_proof/", views.StartHustleProofView.as_view(), name="admin_start_hustle_proof"),
    path("game_health_check/<int:game_episode>", views.RunGameHealthCheck.as_view(), name="game_health_check"),
    path("question_start_time/", views.QuestionStartTime.as_view(), name="question_start_time"),
    path("request_hustle_reveal_question/<int:game_episode>", views.RequestHustNextRevealQuestion.as_view(), name="request_hustle_reveal_question"),
    path("s1_question_time_elapsed/<int:question_id>", views.HustleQuestionTimeElapsed.as_view(), name="s1_question_time_elapsed"),
    path("end_stage_one/<int:game_episode>", views.EndStageOne.as_view(), name="end_stage_one"),
    path("request_proof_hustle_question/<int:game_episode>", views.RequestNextProofHustleQuestions.as_view(), name="get_all_proof_questions"),
     path("s2_question_time_elapsed/<int:question_id>", views.ProofQuestionTimeElapsed.as_view(), name="s2_question_time_elapsed"),
    
    # Needed for test only
    path("test_reassign_questions/<int:game_episode>", views.TestReassignQuestions.as_view(), name="test_reassign_questions"),
    
]