from django.db import models

# Create your models here.
class ConstantTable(models.Model):
    hustle_reveal_max_amount = models.FloatField(default=500000.00, help_text="This is the total amount to be shared for activity one stage one")
    hustle_activity_two_sharing_amount = models.FloatField(default=500000.00, help_text="This is the total amount to be shared for activity two stage one")
    hustle_activity_three_sharing_amount = models.FloatField(default=500000.00, help_text="This is the total amount to be shared for activity two stage one")
    hustle_reveal_sharing_amount = models.FloatField(default=400_000.00, help_text="This is the hustle reveal sharing amount")
    hustle_proof_sharing_amount = models.FloatField(default=1_000_000.00, help_text="This is the proof hustle sharing amount")
    hustle_proof_questions_count = models.IntegerField(default=8)
    hustle_reveal_questions_count = models.IntegerField(default=12)
    hustle_names_count = models.IntegerField(default=49)

    class Meta:
        verbose_name = "Constant Table"
        verbose_name_plural = "Constant Tables"

    def __str__(self):
        return f"Game Constants (ID: {self.id})"
    
class HustleSeason(models.Model):
    season = models.CharField(max_length=255, unique=True)
    year = models.CharField(max_length=100, unique=True)
    description = models.TextField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active_season = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Hustle Season"
        verbose_name_plural = "Hustle Seasons"

    def __str__(self):
        return self.season
    
class HustleNames(models.Model):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    hustle_season = models.ForeignKey(HustleSeason, on_delete=models.CASCADE, related_name="hustle_names", null=True, blank=True)

    class Meta:
        verbose_name = "Hustle Name"
        verbose_name_plural = "Hustle Names"

    def __str__(self):
        return f"{self.name} ({self.hustle_season})"
    
    def save(self, *args, **kwargs):
        if not self.hustle_season:
            raise ValueError("Hustle Season must be set before saving.")
        if not self.hustle_season.is_active_season:
            raise ValueError("Hustle Season must be active before saving.")
        
        super().save(*args, **kwargs)