from rest_framework import serializers

from django.utils.dateparse import parse_datetime

class StartHustleProofSerializer(serializers.Serializer):
    game_episode = serializers.IntegerField()
    
class QuestionStartTimeSerializer(serializers.Serializer):
    start_time = serializers.CharField(required=True)
    question_id = serializers.IntegerField(required=True)
    question_type = serializers.CharField(required=True)

    def validate(self, attrs):
        question_start_time = attrs.get("start_time")
        question_type = attrs.get("question_type")
        if question_type.lower() not in ["stage_1", "stage_2"]:
            raise serializers.ValidationError("Invalid Question Type")
        
        try:
            parsed_time = parse_datetime(question_start_time)
            if not parsed_time:
                raise serializers.ValidationError(
                    "Start Time must be a valid ISO 8601 datetime string (e.g. '2025-05-22T13:22:39.598Z')"
                )
        except ValueError:
           raise serializers.ValidationError("Start Time not valid Time string. Should be '2025-05-22T13:22:39.598Z'")
        
        attrs["question_type"] = question_type.lower()
        attrs["question_start_time"] = question_start_time
        return attrs
    
class ProofContestantAnswerSerializer(serializers.Serializer):
 
    contestant_id = serializers.IntegerField(required=True)
    answer = serializers.CharField(required=True)
    timestamp = serializers.CharField(required=True)

    def validate(self, attrs):
        contestant_id = attrs.get("contestant_id")
        answer = attrs.get("answer")
        timestamp = attrs.get("timestamp")
        
        if not isinstance(contestant_id, int):
            raise serializers.ValidationError("contestant_id must be an integer")
        
        if contestant_id < 1:
            raise serializers.ValidationError("contestant_id must be greater than 0")
        
        if answer.upper() not in ["A", "B", "C", "D", "N"]:
            raise serializers.ValidationError("Answer must be one of 'A', 'B', 'C', 'D', 'N'.")
        
        try:
            parsed_time = parse_datetime(timestamp)
            if not parsed_time:
                raise serializers.ValidationError(
                    "Start Time must be a valid ISO 8601 datetime string (e.g. '2025-05-22T13:22:39.598Z')"
                )
        except ValueError:
           raise serializers.ValidationError("Start Time not valid Time string. Should be '2025-05-22T13:22:39.598Z'")
        
        attrs["timestamp"] = timestamp
        return attrs